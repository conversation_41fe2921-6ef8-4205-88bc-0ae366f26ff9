import numpy as np
import json
from fov_calculator import create_fov_calculator

def test_csgo_fov():
    """测试CSGO的FOV计算"""
    
    # 加载配置
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("=== CSGO FOV计算测试 ===")
    print(f"游戏FOV: {config['game_settings']['fov']}°")
    print(f"游戏敏感度: {config['game_settings']['sensitivity']}")
    print(f"游戏类型: {config['game_settings']['game_type']}")
    print(f"FOV补偿: {'启用' if config['game_settings']['enable_fov_compensation'] else '禁用'}")
    print()
    
    # 创建FOV计算器
    fov_calculator = create_fov_calculator(config)
    
    # 测试不同的像素误差
    test_cases = [
        np.array([10, 0]),    # 水平10像素
        np.array([0, 10]),    # 垂直10像素
        np.array([50, 0]),    # 水平50像素
        np.array([0, 50]),    # 垂直50像素
        np.array([100, 0]),   # 水平100像素
        np.array([0, 100]),   # 垂直100像素
        np.array([50, 50]),   # 对角线50像素
    ]
    
    print("像素误差 -> 角度误差 -> 鼠标移动量")
    print("-" * 60)
    
    for pixel_error in test_cases:
        # 计算角度误差
        angle_error = fov_calculator.pixel_to_angle(pixel_error)
        
        # 计算鼠标移动量
        mouse_movement = fov_calculator.pixel_to_mouse_movement(pixel_error, is_ads=False)
        
        print(f"像素: ({pixel_error[0]:3.0f}, {pixel_error[1]:3.0f}) -> "
              f"角度: ({angle_error[0]:6.3f}°, {angle_error[1]:6.3f}°) -> "
              f"鼠标: ({mouse_movement[0]:6.1f}, {mouse_movement[1]:6.1f})")
    
    print()
    print("=== FOV补偿效果对比 ===")
    
    # 对比启用和禁用FOV补偿的效果
    test_pixel = np.array([100, 0])  # 100像素水平误差
    
    # 启用FOV补偿
    config['game_settings']['enable_fov_compensation'] = True
    fov_calc_enabled = create_fov_calculator(config)
    mouse_with_fov = fov_calc_enabled.pixel_to_mouse_movement(test_pixel)
    
    # 禁用FOV补偿
    config['game_settings']['enable_fov_compensation'] = False
    fov_calc_disabled = create_fov_calculator(config)
    mouse_without_fov = fov_calc_disabled.pixel_to_mouse_movement(test_pixel)
    
    print(f"100像素水平误差:")
    print(f"  启用FOV补偿: 鼠标移动 ({mouse_with_fov[0]:.1f}, {mouse_with_fov[1]:.1f})")
    print(f"  禁用FOV补偿: 鼠标移动 ({mouse_without_fov[0]:.1f}, {mouse_without_fov[1]:.1f})")
    print(f"  补偿系数: {mouse_with_fov[0] / mouse_without_fov[0]:.3f}")
    
    print()
    print("=== 不同敏感度下的效果 ===")
    
    # 恢复FOV补偿
    config['game_settings']['enable_fov_compensation'] = True
    
    sensitivities = [1.0, 1.5, 2.0, 2.5, 3.0]
    test_pixel = np.array([50, 0])
    
    for sens in sensitivities:
        config['game_settings']['sensitivity'] = sens
        fov_calc = create_fov_calculator(config)
        mouse_movement = fov_calc.pixel_to_mouse_movement(test_pixel)
        print(f"敏感度 {sens:.1f}: 50像素 -> 鼠标移动 ({mouse_movement[0]:.1f}, {mouse_movement[1]:.1f})")

if __name__ == "__main__":
    test_csgo_fov()
