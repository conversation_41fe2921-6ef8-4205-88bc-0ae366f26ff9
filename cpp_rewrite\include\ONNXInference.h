#pragma once

#include <opencv2/opencv.hpp>
#include <onnxruntime_cxx_api.h>
#include <vector>
#include <string>
#include <memory>
#include <chrono>

/**
 * ONNX模型推理类 - 支持DirectML GPU加速
 */
class ONNXInference {
public:
    /**
     * 构造函数
     * @param modelPath 模型文件路径
     * @param useGPU 是否使用GPU加速 (DirectML)
     */
    ONNXInference(const std::string& modelPath = "", bool useGPU = true);

    /**
     * 析构函数
     */
    ~ONNXInference();

    /**
     * 初始化推理引擎
     * @param modelPath 模型文件路径
     * @param useGPU 是否使用GPU加速
     * @return 成功返回true
     */
    bool Initialize(const std::string& modelPath, bool useGPU = true);

    /**
     * 执行推理
     * @param inputImage 输入图像 (RGB格式，320x320)
     * @return 推理结果张量，失败返回空vector
     */
    std::vector<float> Inference(const cv::Mat& inputImage);

    /**
     * 获取输入尺寸
     */
    std::pair<int, int> GetInputSize() const { return {m_inputWidth, m_inputHeight}; }

    /**
     * 获取输出形状
     */
    std::vector<int64_t> GetOutputShape() const { return m_outputShape; }

    /**
     * 检查是否已初始化
     */
    bool IsInitialized() const { return m_initialized; }

    /**
     * 获取推理时间统计
     */
    double GetLastInferenceTime() const { return m_lastInferenceTime; }
    double GetAverageInferenceTime() const { return m_avgInferenceTime; }
    int GetInferenceCount() const { return m_inferenceCount; }

    /**
     * 获取使用的执行提供程序
     */
    std::string GetExecutionProvider() const { return m_executionProvider; }

private:
    // ONNX Runtime相关
    std::unique_ptr<Ort::Env> m_env;
    std::unique_ptr<Ort::Session> m_session;
    std::unique_ptr<Ort::SessionOptions> m_sessionOptions;
    Ort::AllocatorWithDefaultOptions m_allocator;

    // 模型信息
    std::string m_modelPath;
    std::vector<const char*> m_inputNames;
    std::vector<const char*> m_outputNames;
    std::vector<std::string> m_inputNameStrings;
    std::vector<std::string> m_outputNameStrings;
    std::vector<int64_t> m_inputShape;
    std::vector<int64_t> m_outputShape;

    // 输入输出尺寸
    int m_inputWidth;
    int m_inputHeight;
    int m_inputChannels;

    // 状态
    bool m_initialized;
    bool m_useGPU;
    std::string m_executionProvider;

    // 性能统计
    mutable double m_lastInferenceTime;
    mutable double m_avgInferenceTime;
    mutable int m_inferenceCount;

    // 私有方法
    bool LoadModel(const std::string& modelPath);
    void GetModelInfo();
    std::vector<float> PreprocessImage(const cv::Mat& image);
    void UpdatePerformanceStats(double inferenceTime) const;
};
