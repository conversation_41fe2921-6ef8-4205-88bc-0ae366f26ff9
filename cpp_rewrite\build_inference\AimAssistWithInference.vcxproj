﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{92FB03D8-53BB-3987-B72F-DEA08F960C2D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>AimAssistWithInference</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AimAssistWithInference.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">AimAssistWithInference</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AimAssistWithInference.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">AimAssistWithInference</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">AimAssistWithInference.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">AimAssistWithInference</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">AimAssistWithInference.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">AimAssistWithInference</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\onnxruntime-directml\lib\onnxruntime.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/Debug/AimAssistWithInference.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Debug/AimAssistWithInference.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\onnxruntime-directml\lib\onnxruntime.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/Release/AimAssistWithInference.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/Release/AimAssistWithInference.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\onnxruntime-directml\lib\onnxruntime.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/MinSizeRel/AimAssistWithInference.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/MinSizeRel/AimAssistWithInference.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\include;D:\3rd_party\onnxruntime-directml\include;C:\Users\<USER>\Desktop\foye_111111\3rdparty\Microsoft.AI.DirectML.1.15.4\include;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\..\3rdparty;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>		</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1/runtimes/win-x64/native/onnxruntime_providers_shared.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/../3rdparty/Microsoft.AI.DirectML.1.15.4/bin/x64-win/DirectML.dll C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\onnxruntime-directml\lib\onnxruntime.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/RelWithDebInfo/AimAssistWithInference.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/bin/RelWithDebInfo/AimAssistWithInference.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite -BC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite -BC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite -BC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite -BC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\capture_with_inference.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\src\ONNXInference.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\foye_111111\cpp_rewrite\build_inference\ZERO_CHECK.vcxproj">
      <Project>{92925876-5876-3E30-B6FD-0C87C57FCDE9}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>