#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件测试脚本
用于验证配置文件是否正确加载
"""

import json
import os
from config_loader import Config<PERSON>oader

def test_config_loading():
    """测试配置文件加载"""
    print("=== 配置文件测试 ===\n")
    
    # 测试1: 加载默认配置文件
    print("1. 测试加载 config.json")
    loader = ConfigLoader("config.json")
    config = loader.load_config()
    
    if config:
        print("✅ 配置文件加载成功!")
        loader.print_config_summary()
    else:
        print("❌ 配置文件加载失败!")
        return False
    
    # 测试2: 验证关键配置项
    print("\n2. 验证关键配置项")
    required_keys = [
        "model", "capture", "mouse", "pid", "display", "debug"
    ]
    
    for key in required_keys:
        if key in config:
            print(f"✅ {key}: 存在")
        else:
            print(f"❌ {key}: 缺失")
            return False
    
    # 测试3: 验证扳机配置
    print("\n3. 验证扳机配置")
    aim_keys = config.get("mouse", {}).get("aim_keys", {})
    
    for prio_name, keys in aim_keys.items():
        print(f"\n{prio_name}:")
        for i, key_cfg in enumerate(keys):
            key_code = key_cfg.get("key", "未知")
            trigger = key_cfg.get("trigger", {})
            enabled = trigger.get("enabled", False)
            
            if enabled:
                delay = trigger.get("fire_delay_ms", 0)
                interval = trigger.get("fire_interval_ms", 0)
                print(f"  按键{key_code}: ✅ 扳机启用 (延迟{delay}ms, 间隔{interval}ms)")
            else:
                print(f"  按键{key_code}: ⚪ 扳机禁用")
    
    # 测试4: 验证PID配置
    print("\n4. 验证PID配置")
    pid_config = config.get("pid", {})
    move_modes = ["system", "kmbox", "ghub"]
    
    for mode in move_modes:
        if mode in pid_config:
            pid_params = pid_config[mode]
            kp = pid_params.get("kp", 0)
            ki = pid_params.get("ki", 0)
            kd = pid_params.get("kd", 0)
            print(f"  {mode}: kp={kp}, ki={ki}, kd={kd}")
        else:
            print(f"  {mode}: ❌ 配置缺失")
    
    print(f"\n✅ 配置文件测试完成!")
    return True

def validate_json_format():
    """验证JSON格式"""
    print("\n=== JSON格式验证 ===")
    
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            json.load(f)
        print("✅ JSON格式正确")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except FileNotFoundError:
        print("❌ 配置文件不存在")
        return False

def create_backup_config():
    """创建配置文件备份"""
    print("\n=== 创建配置备份 ===")
    
    if os.path.exists("config.json"):
        try:
            import shutil
            backup_name = "config_backup.json"
            shutil.copy2("config.json", backup_name)
            print(f"✅ 配置备份已创建: {backup_name}")
            return True
        except Exception as e:
            print(f"❌ 创建备份失败: {e}")
            return False
    else:
        print("❌ 原配置文件不存在")
        return False

def show_config_tips():
    """显示配置建议"""
    print("\n=== 配置建议 ===")
    print("🎯 新手建议:")
    print("  - confidence_threshold: 0.7 (较高精度)")
    print("  - 先禁用扳机功能进行测试")
    print("  - 使用较低的PID参数")
    
    print("\n⚡ 高级用户:")
    print("  - confidence_threshold: 0.6-0.65")
    print("  - 启用扳机并调整延迟参数")
    print("  - 根据硬件调整PID参数")
    
    print("\n🔧 调试技巧:")
    print("  - 开启 debug.show_timing 查看性能")
    print("  - 调整 aim_circle_radius 改变瞄准范围")
    print("  - 修改 control_frequency 调整响应速度")

if __name__ == "__main__":
    print("配置文件测试工具")
    print("=" * 50)
    
    # 验证JSON格式
    if not validate_json_format():
        print("\n请先修复JSON格式错误!")
        exit(1)
    
    # 测试配置加载
    if not test_config_loading():
        print("\n配置文件测试失败!")
        exit(1)
    
    # 创建备份
    create_backup_config()
    
    # 显示建议
    show_config_tips()
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过! 配置文件可以正常使用。")
    print("💡 提示: 修改配置后重启主程序即可生效。")
