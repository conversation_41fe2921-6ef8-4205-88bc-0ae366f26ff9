import cv2
import numpy as np
import json
import time
from typing import Dict, Any, Tu<PERSON>, Optional
from fov_calculator import create_fov_calculator

class FOVCalibrationTool:
    """
    FOV校准工具，帮助用户校准游戏FOV和敏感度设置
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化校准工具
        
        参数:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.load_config()
        self.fov_calculator = create_fov_calculator(self.config)
        
        # 校准状态
        self.calibration_points = []
        self.is_calibrating = False
        self.current_step = 0
        self.calibration_steps = [
            "请将准心对准屏幕中心的目标",
            "请移动鼠标使准心对准右侧目标",
            "请移动鼠标使准心对准左侧目标",
            "请移动鼠标使准心对准上方目标",
            "请移动鼠标使准心对准下方目标"
        ]
        
        print("[FOV校准工具] 初始化完成")
        print("按 'c' 开始校准，按 'q' 退出")
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            # 使用默认配置
            self.config = {
                "game_settings": {
                    "fov": 90,
                    "sensitivity": 1.0,
                    "ads_sensitivity_multiplier": 1.0,
                    "enable_fov_compensation": True,
                    "game_type": "pubg"
                },
                "capture": {
                    "screen_resolution": [1920, 1080],
                    "roi_size": 640
                }
            }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            print(f"配置已保存到 {self.config_path}")
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def draw_calibration_targets(self, img: np.ndarray) -> np.ndarray:
        """
        在图像上绘制校准目标
        
        参数:
            img: 输入图像
            
        返回:
            绘制了目标的图像
        """
        h, w = img.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # 目标距离（像素）
        target_distance = min(w, h) // 4
        
        # 绘制中心十字准心
        cv2.line(img, (center_x - 20, center_y), (center_x + 20, center_y), (0, 255, 0), 2)
        cv2.line(img, (center_x, center_y - 20), (center_x, center_y + 20), (0, 255, 0), 2)
        
        # 绘制校准目标
        targets = [
            (center_x, center_y),  # 中心
            (center_x + target_distance, center_y),  # 右
            (center_x - target_distance, center_y),  # 左
            (center_x, center_y - target_distance),  # 上
            (center_x, center_y + target_distance)   # 下
        ]
        
        for i, (x, y) in enumerate(targets):
            color = (0, 0, 255) if i == self.current_step else (255, 255, 255)
            cv2.circle(img, (x, y), 10, color, -1)
            cv2.circle(img, (x, y), 15, color, 2)
            
            # 标注目标编号
            cv2.putText(img, str(i), (x - 5, y + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return img
    
    def draw_instructions(self, img: np.ndarray) -> np.ndarray:
        """
        在图像上绘制指令文本
        
        参数:
            img: 输入图像
            
        返回:
            绘制了指令的图像
        """
        h, w = img.shape[:2]
        
        # 绘制背景
        cv2.rectangle(img, (10, 10), (w - 10, 120), (0, 0, 0), -1)
        cv2.rectangle(img, (10, 10), (w - 10, 120), (255, 255, 255), 2)
        
        # 绘制指令文本
        if self.is_calibrating and self.current_step < len(self.calibration_steps):
            instruction = self.calibration_steps[self.current_step]
            cv2.putText(img, instruction, (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(img, f"Step {self.current_step + 1}/{len(self.calibration_steps)}", (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(img, "Press SPACE to record position", (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        else:
            cv2.putText(img, "FOV Calibration Tool", (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(img, "Press 'c' to start calibration", (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            cv2.putText(img, "Press 'q' to quit", (20, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        return img
    
    def record_calibration_point(self, mouse_pos: Tuple[int, int]):
        """
        记录校准点
        
        参数:
            mouse_pos: 鼠标位置 (x, y)
        """
        self.calibration_points.append({
            'step': self.current_step,
            'mouse_pos': mouse_pos,
            'timestamp': time.time()
        })
        
        print(f"记录校准点 {self.current_step + 1}: {mouse_pos}")
        
        self.current_step += 1
        
        if self.current_step >= len(self.calibration_steps):
            self.finish_calibration()
    
    def finish_calibration(self):
        """完成校准并计算结果"""
        print("\n校准完成！正在计算结果...")
        
        if len(self.calibration_points) < 5:
            print("校准点不足，无法计算结果")
            self.reset_calibration()
            return
        
        # 计算鼠标移动距离
        center_pos = self.calibration_points[0]['mouse_pos']
        
        movements = []
        for i in range(1, len(self.calibration_points)):
            point = self.calibration_points[i]
            dx = point['mouse_pos'][0] - center_pos[0]
            dy = point['mouse_pos'][1] - center_pos[1]
            distance = np.sqrt(dx**2 + dy**2)
            movements.append(distance)
        
        # 计算平均移动距离
        avg_movement = np.mean(movements)
        
        # 计算建议的敏感度
        roi_size = self.config["capture"]["roi_size"]
        target_distance_pixels = roi_size // 4  # 目标距离
        
        # 使用FOV计算器进行校准
        suggested_sensitivity = self.fov_calculator.calibrate_sensitivity(
            target_distance_pixels, avg_movement, is_ads=False
        )
        
        print(f"\n校准结果:")
        print(f"平均鼠标移动距离: {avg_movement:.1f} 像素")
        print(f"目标像素距离: {target_distance_pixels} 像素")
        print(f"建议敏感度: {suggested_sensitivity:.3f}")
        
        # 询问是否应用设置
        print("\n是否应用建议的敏感度设置？(y/n): ", end="")
        
        self.suggested_sensitivity = suggested_sensitivity
        self.is_calibrating = False
    
    def apply_calibration_result(self):
        """应用校准结果"""
        if hasattr(self, 'suggested_sensitivity'):
            self.config["game_settings"]["sensitivity"] = self.suggested_sensitivity
            self.fov_calculator.update_settings(self.config["game_settings"])
            self.save_config()
            print("敏感度设置已应用并保存")
        else:
            print("没有可应用的校准结果")
    
    def reset_calibration(self):
        """重置校准状态"""
        self.calibration_points = []
        self.is_calibrating = False
        self.current_step = 0
        print("校准已重置")
    
    def start_calibration(self):
        """开始校准"""
        self.reset_calibration()
        self.is_calibrating = True
        print("开始FOV校准...")
        print("请按照屏幕指示进行操作")
    
    def run(self):
        """运行校准工具"""
        # 创建一个简单的校准界面
        img = np.zeros((600, 800, 3), dtype=np.uint8)
        
        cv2.namedWindow("FOV Calibration Tool", cv2.WINDOW_NORMAL)
        cv2.setWindowProperty("FOV Calibration Tool", cv2.WND_PROP_TOPMOST, 1)
        
        while True:
            # 创建新的图像
            img = np.zeros((600, 800, 3), dtype=np.uint8)
            
            # 绘制校准目标和指令
            img = self.draw_calibration_targets(img)
            img = self.draw_instructions(img)
            
            # 显示当前FOV设置
            fov_text = f"Current FOV: {self.config['game_settings']['fov']}°"
            sens_text = f"Current Sensitivity: {self.config['game_settings']['sensitivity']:.3f}"
            cv2.putText(img, fov_text, (20, img.shape[0] - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            cv2.putText(img, sens_text, (20, img.shape[0] - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            cv2.imshow("FOV Calibration Tool", img)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                break
            elif key == ord('c') and not self.is_calibrating:
                self.start_calibration()
            elif key == ord(' ') and self.is_calibrating:
                # 模拟记录鼠标位置（实际应用中需要获取真实鼠标位置）
                mouse_x, mouse_y = cv2.getWindowImageRect("FOV Calibration Tool")[:2]
                self.record_calibration_point((mouse_x, mouse_y))
            elif key == ord('y') and hasattr(self, 'suggested_sensitivity'):
                self.apply_calibration_result()
            elif key == ord('n') and hasattr(self, 'suggested_sensitivity'):
                print("校准结果已丢弃")
                delattr(self, 'suggested_sensitivity')
            elif key == ord('r'):
                self.reset_calibration()
        
        cv2.destroyAllWindows()

def main():
    """主函数"""
    calibration_tool = FOVCalibrationTool()
    calibration_tool.run()

if __name__ == "__main__":
    main()
