﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8E5582DE-331C-3E77-99EA-F50C68558F30}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>test_capture</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">test_capture.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">test_capture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">test_capture.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">test_capture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">test_capture.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">test_capture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">test_capture.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">test_capture</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480d.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>lib\Debug\AimAssist.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build_capture/Debug/test_capture.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/Debug/test_capture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480d.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>lib\Release\AimAssist.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build_capture/Release/test_capture.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/Release/test_capture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480d.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>lib\MinSizeRel\AimAssist.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build_capture/MinSizeRel/test_capture.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/MinSizeRel/test_capture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>false</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\foye_111111\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
D:\C++\bin\cmake.exe -E copy_if_different D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/bin/opencv_world480d.dll C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>lib\RelWithDebInfo\AimAssist.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;d3d11.lib;dxgi.lib;user32.lib;gdi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build_capture/RelWithDebInfo/test_capture.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build_capture/bin/RelWithDebInfo/test_capture.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\foye_111111\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111 -BC:/Users/<USER>/Desktop/foye_111111/build_capture --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build_capture/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPkgConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111 -BC:/Users/<USER>/Desktop/foye_111111/build_capture --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build_capture/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPkgConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111 -BC:/Users/<USER>/Desktop/foye_111111/build_capture --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build_capture/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPkgConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_111111/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111 -BC:/Users/<USER>/Desktop/foye_111111/build_capture --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build_capture/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPkgConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build_capture\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\foye_111111\test\test_capture.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\foye_111111\build_capture\ZERO_CHECK.vcxproj">
      <Project>{657D5982-5DEA-3249-A6BB-B87DC13748E6}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\foye_111111\build_capture\AimAssist_CPP_lib.vcxproj">
      <Project>{424311D7-1F7F-36EE-BC75-2128F522963B}</Project>
      <Name>AimAssist_CPP_lib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>