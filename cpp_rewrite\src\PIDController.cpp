#include "../include/PIDController.h"

PIDController::PIDController(const PIDParams& params)
    : m_params(params)
    , m_lastError(0, 0)
    , m_integral(0, 0)
    , m_lastTime(0.0)
    , m_firstRun(true)
{
}

cv::Point2f PIDController::Compute(const cv::Point2f& error, double deltaTime) {
    if (m_firstRun) {
        m_lastError = error;
        m_firstRun = false;
        return cv::Point2f(0, 0);
    }

    // 计算积分项
    m_integral.x += error.x * deltaTime;
    m_integral.y += error.y * deltaTime;

    // 计算微分项
    cv::Point2f derivative;
    if (deltaTime > 0) {
        derivative.x = (error.x - m_lastError.x) / deltaTime;
        derivative.y = (error.y - m_lastError.y) / deltaTime;
    }

    // 获取自适应积分系数
    float currentKi = GetCurrentKi(error);

    // 计算PID输出
    cv::Point2f output;
    output.x = m_params.kp * error.x + currentKi * m_integral.x + m_params.kd * derivative.x;
    output.y = m_params.kp * error.y + currentKi * m_integral.y + m_params.kd * derivative.y;

    // 限制输出
    output = ClampOutput(output);

    // 更新状态
    m_lastError = error;

    return output;
}

void PIDController::Reset() {
    m_lastError = cv::Point2f(0, 0);
    m_integral = cv::Point2f(0, 0);
    m_firstRun = true;
}

void PIDController::UpdateParams(const PIDParams& params) {
    m_params = params;
}

float PIDController::GetCurrentKi(const cv::Point2f& error) const {
    float errorMagnitude = std::sqrt(error.x * error.x + error.y * error.y);
    
    if (errorMagnitude <= m_params.innerThreshold) {
        // 内圈：使用最大积分系数
        return m_params.maxKi;
    }
    else if (errorMagnitude <= m_params.outerThreshold) {
        // 中圈：线性插值
        float ratio = (m_params.outerThreshold - errorMagnitude) / 
                     (m_params.outerThreshold - m_params.innerThreshold);
        return m_params.baseKi + ratio * (m_params.maxKi - m_params.baseKi);
    }
    else {
        // 外圈：使用基础积分系数
        return m_params.baseKi;
    }
}

PIDController::ControllerState PIDController::GetState() const {
    ControllerState state;
    state.lastError = m_lastError;
    state.integral = m_integral;
    state.derivative = cv::Point2f(0, 0); // 微分项不保存状态
    state.output = cv::Point2f(0, 0);     // 输出不保存状态
    state.currentKi = GetCurrentKi(m_lastError);
    state.lastTime = m_lastTime;
    return state;
}

float PIDController::CalculateAdaptiveKi(const cv::Point2f& error) const {
    return GetCurrentKi(error);
}

cv::Point2f PIDController::ClampOutput(const cv::Point2f& output) const {
    // 限制输出范围 (防止过大的移动)
    const float maxOutput = 100.0f;
    
    cv::Point2f clampedOutput = output;
    clampedOutput.x = Utils::Clamp(clampedOutput.x, -maxOutput, maxOutput);
    clampedOutput.y = Utils::Clamp(clampedOutput.y, -maxOutput, maxOutput);
    
    return clampedOutput;
}

// AdvancedPIDController 实现
AdvancedPIDController::AdvancedPIDController(const PIDParams& params)
    : PIDController(params)
    , m_minOutput(-100, -100)
    , m_maxOutput(100, 100)
    , m_hasOutputLimits(false)
    , m_maxIntegral(1000.0f)
    , m_hasIntegralLimits(false)
    , m_useDerivativeFilter(false)
    , m_derivativeFilterCoeff(0.1f)
    , m_filteredDerivative(0, 0)
{
}

cv::Point2f AdvancedPIDController::ComputeAdvanced(
    const cv::Point2f& error,
    double deltaTime,
    const cv::Point2f& targetVelocity
) {
    // 基础PID计算
    cv::Point2f basicOutput = Compute(error, deltaTime);
    
    // 添加前馈控制
    cv::Point2f feedforward = targetVelocity * 0.1f; // 简单的前馈项
    
    cv::Point2f totalOutput = basicOutput + feedforward;
    
    // 应用输出限制
    if (m_hasOutputLimits) {
        totalOutput.x = Utils::Clamp(totalOutput.x, m_minOutput.x, m_maxOutput.x);
        totalOutput.y = Utils::Clamp(totalOutput.y, m_minOutput.y, m_maxOutput.y);
    }
    
    return totalOutput;
}

void AdvancedPIDController::SetOutputLimits(const cv::Point2f& minOutput, const cv::Point2f& maxOutput) {
    m_minOutput = minOutput;
    m_maxOutput = maxOutput;
    m_hasOutputLimits = true;
}

void AdvancedPIDController::SetIntegralLimits(float maxIntegral) {
    m_maxIntegral = maxIntegral;
    m_hasIntegralLimits = true;
}

void AdvancedPIDController::SetDerivativeFilter(bool enable, float filterCoeff) {
    m_useDerivativeFilter = enable;
    m_derivativeFilterCoeff = filterCoeff;
}

cv::Point2f AdvancedPIDController::ApplyDerivativeFilter(const cv::Point2f& derivative) {
    if (!m_useDerivativeFilter) {
        return derivative;
    }
    
    // 简单的低通滤波器
    m_filteredDerivative.x = m_filteredDerivative.x * (1.0f - m_derivativeFilterCoeff) + 
                            derivative.x * m_derivativeFilterCoeff;
    m_filteredDerivative.y = m_filteredDerivative.y * (1.0f - m_derivativeFilterCoeff) + 
                            derivative.y * m_derivativeFilterCoeff;
    
    return m_filteredDerivative;
}

cv::Point2f AdvancedPIDController::ClampIntegral(const cv::Point2f& integral) const {
    if (!m_hasIntegralLimits) {
        return integral;
    }
    
    cv::Point2f clampedIntegral = integral;
    clampedIntegral.x = Utils::Clamp(clampedIntegral.x, -m_maxIntegral, m_maxIntegral);
    clampedIntegral.y = Utils::Clamp(clampedIntegral.y, -m_maxIntegral, m_maxIntegral);
    
    return clampedIntegral;
}
