cmake_minimum_required(VERSION 3.16)
project(AimAssist_CPP VERSION 1.0.0)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 编译选项
if(MSVC)
    add_compile_options(/W4 /WX- /permissive-)
    # 优化选项
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /Ob2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")
endif()

# 查找OpenCV
set(OpenCV_DIR "D:/3rd_party/opencv_4.8/opencv/build")
find_package(OpenCV REQUIRED)

if(OpenCV_FOUND)
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    message(STATUS "OpenCV include dirs: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
else()
    message(FATAL_ERROR "OpenCV not found!")
endif()

# 查找Windows SDK
find_package(PkgConfig QUIET)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
)

# 源文件
set(SOURCES
    src/ScreenCapture.cpp
)

# 头文件
set(HEADERS
    include/ScreenCapture.h
)

# 创建静态库
add_library(${PROJECT_NAME}_lib STATIC ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}_lib
    ${OpenCV_LIBS}
    d3d11.lib
    dxgi.lib
    user32.lib
    gdi32.lib
)

# 设置目标属性
set_target_properties(${PROJECT_NAME}_lib PROPERTIES
    OUTPUT_NAME "AimAssist"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 创建测试可执行文件
add_executable(test_capture test/test_capture.cpp)
target_link_libraries(test_capture ${PROJECT_NAME}_lib)

# 设置可执行文件输出目录
set_target_properties(test_capture PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 安装规则
install(TARGETS ${PROJECT_NAME}_lib
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${HEADERS}
    DESTINATION include
)

# 复制OpenCV DLL到输出目录 (仅Release模式)
if(WIN32 AND CMAKE_BUILD_TYPE STREQUAL "Release")
    # 查找OpenCV DLL
    get_filename_component(OpenCV_BIN_DIR "${OpenCV_DIR}/x64/vc16/bin" ABSOLUTE)
    
    if(EXISTS "${OpenCV_BIN_DIR}")
        file(GLOB OpenCV_DLLS "${OpenCV_BIN_DIR}/opencv_world*.dll")
        foreach(dll ${OpenCV_DLLS})
            add_custom_command(TARGET test_capture POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${dll}" $<TARGET_FILE_DIR:test_capture>
            )
        endforeach()
    endif()
endif()

# 打印配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
