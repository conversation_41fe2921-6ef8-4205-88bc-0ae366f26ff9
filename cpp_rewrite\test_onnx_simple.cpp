#include <iostream>
#include <onnxruntime_cxx_api.h>

int main() {
    try {
        std::cout << "=== Simple ONNX Runtime Test ===" << std::endl;
        
        // 测试1: 创建环境
        std::cout << "Creating ONNX Runtime environment..." << std::endl;
        Ort::Env env(ORT_LOGGING_LEVEL_WARNING, "TestApp");
        std::cout << "Environment created successfully!" << std::endl;
        
        // 测试2: 创建会话选项
        std::cout << "Creating session options..." << std::endl;
        Ort::SessionOptions sessionOptions;
        sessionOptions.SetIntraOpNumThreads(1);
        sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);
        std::cout << "Session options created successfully!" << std::endl;
        
        // 测试3: 尝试加载模型
        std::cout << "Loading model..." << std::endl;
        std::wstring modelPath = L"C:\\Users\\<USER>\\Desktop\\foye_111111\\models\\PUBGV8_320.onnx";
        Ort::Session session(env, modelPath.c_str(), sessionOptions);
        std::cout << "Model loaded successfully!" << std::endl;

        // 测试4: 获取模型信息
        std::cout << "Getting model info..." << std::endl;
        Ort::AllocatorWithDefaultOptions allocator;

        size_t numInputNodes = session.GetInputCount();
        size_t numOutputNodes = session.GetOutputCount();

        std::cout << "Input nodes: " << numInputNodes << std::endl;
        std::cout << "Output nodes: " << numOutputNodes << std::endl;

        // 获取输入信息
        for (size_t i = 0; i < numInputNodes; i++) {
            auto inputName = session.GetInputNameAllocated(i, allocator);
            auto inputTypeInfo = session.GetInputTypeInfo(i);
            auto inputTensorInfo = inputTypeInfo.GetTensorTypeAndShapeInfo();
            auto inputShape = inputTensorInfo.GetShape();

            std::cout << "Input " << i << ": " << inputName.get() << " shape: [";
            for (size_t j = 0; j < inputShape.size(); j++) {
                std::cout << inputShape[j];
                if (j < inputShape.size() - 1) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }

        // 获取输出信息
        for (size_t i = 0; i < numOutputNodes; i++) {
            auto outputName = session.GetOutputNameAllocated(i, allocator);
            auto outputTypeInfo = session.GetOutputTypeInfo(i);
            auto outputTensorInfo = outputTypeInfo.GetTensorTypeAndShapeInfo();
            auto outputShape = outputTensorInfo.GetShape();

            std::cout << "Output " << i << ": " << outputName.get() << " shape: [";
            for (size_t j = 0; j < outputShape.size(); j++) {
                std::cout << outputShape[j];
                if (j < outputShape.size() - 1) std::cout << ", ";
            }
            std::cout << "]" << std::endl;
        }
        
        std::cout << "All ONNX Runtime tests passed!" << std::endl;
        return 0;
        
    } catch (const Ort::Exception& e) {
        std::cerr << "ONNX Runtime exception: " << e.what() << std::endl;
        return -1;
    } catch (const std::exception& e) {
        std::cerr << "Standard exception: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return -1;
    }
}
