#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <iostream>
#include <chrono>
#include <vector>
#include <thread>
#include "include/ONNXInference.h"

#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

class CaptureWithInference {
private:
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    int m_screenWidth;
    int m_screenHeight;
    int m_roiX, m_roiY;
    int m_roiSize;
    bool m_initialized;
    
    std::unique_ptr<ONNXInference> m_inference;

public:
    CaptureWithInference(int roiSize = 320) 
        : m_pDevice(nullptr)
        , m_pContext(nullptr)
        , m_pDeskDupl(nullptr)
        , m_pStagingTexture(nullptr)
        , m_roiSize(roiSize)
        , m_initialized(false)
    {
    }

    ~CaptureWithInference() {
        Release();
    }

    bool Initialize(const std::string& modelPath = "") {
        if (m_initialized) {
            return true;
        }

        // 获取屏幕尺寸
        m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
        m_screenHeight = GetSystemMetrics(SM_CYSCREEN);
        
        // 计算屏幕中央ROI
        m_roiX = (m_screenWidth - m_roiSize) / 2;
        m_roiY = (m_screenHeight - m_roiSize) / 2;
        
        std::cout << "Screen size: " << m_screenWidth << "x" << m_screenHeight << std::endl;
        std::cout << "ROI center: (" << m_roiX << ", " << m_roiY << ") size: " << m_roiSize << "x" << m_roiSize << std::endl;

        // 初始化DXGI
        if (!InitializeDXGI()) {
            std::cerr << "Failed to initialize DXGI" << std::endl;
            return false;
        }

        // 初始化ONNX推理（如果提供了模型路径）
        if (!modelPath.empty()) {
            m_inference = std::make_unique<ONNXInference>();
            if (!m_inference->Initialize(modelPath, true)) {
                std::cerr << "Failed to initialize ONNX inference" << std::endl;
                m_inference.reset();
            } else {
                std::cout << "ONNX inference initialized successfully" << std::endl;
            }
        }

        m_initialized = true;
        std::cout << "CaptureWithInference initialized successfully" << std::endl;
        return true;
    }

    cv::Mat CaptureROI() {
        if (!m_initialized) {
            return cv::Mat();
        }

        // 获取完整屏幕截图
        std::vector<BYTE> frameBuffer;
        int width, height;
        
        HRESULT hr = GetNextFrame(frameBuffer, width, height);
        if (FAILED(hr) || hr == S_FALSE) {
            return cv::Mat();
        }

        // 创建完整屏幕的Mat (BGRA格式)
        cv::Mat fullScreen(height, width, CV_8UC4, frameBuffer.data());
        
        // 提取ROI区域
        cv::Rect roi(m_roiX, m_roiY, m_roiSize, m_roiSize);
        
        // 确保ROI在屏幕范围内
        roi.x = std::max(0, std::min(roi.x, width - 1));
        roi.y = std::max(0, std::min(roi.y, height - 1));
        roi.width = std::min(roi.width, width - roi.x);
        roi.height = std::min(roi.height, height - roi.y);
        
        cv::Mat roiFrame = fullScreen(roi);
        
        // 转换为RGB格式
        cv::Mat rgbFrame;
        cv::cvtColor(roiFrame, rgbFrame, cv::COLOR_BGRA2RGB);
        
        return rgbFrame.clone();
    }

    std::vector<float> CaptureAndInference() {
        if (!m_inference) {
            return {};
        }

        cv::Mat frame = CaptureROI();
        if (frame.empty()) {
            return {};
        }

        return m_inference->Inference(frame);
    }

    bool HasInference() const {
        return m_inference != nullptr;
    }

    const ONNXInference* GetInference() const {
        return m_inference.get();
    }

    void Release() {
        m_inference.reset();
        
        SAFE_RELEASE(m_pStagingTexture);
        SAFE_RELEASE(m_pDeskDupl);
        SAFE_RELEASE(m_pContext);
        SAFE_RELEASE(m_pDevice);
        
        m_initialized = false;
        std::cout << "CaptureWithInference resources released" << std::endl;
    }

private:
    bool InitializeDXGI() {
        HRESULT hr = D3D11CreateDevice(
            nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
            nullptr, 0, D3D11_SDK_VERSION,
            &m_pDevice, nullptr, &m_pContext
        );
        
        if (FAILED(hr)) {
            std::cerr << "Failed to create D3D11 device: 0x" << std::hex << hr << std::endl;
            return false;
        }
        
        // 获取DXGI链
        IDXGIDevice* pDxgiDevice = nullptr;
        hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
        if (FAILED(hr)) return false;
        
        IDXGIAdapter* pDxgiAdapter = nullptr;
        hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
        SAFE_RELEASE(pDxgiDevice);
        if (FAILED(hr)) return false;
        
        IDXGIOutput* pDxgiOutput = nullptr;
        hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
        SAFE_RELEASE(pDxgiAdapter);
        if (FAILED(hr)) return false;
        
        IDXGIOutput1* pDxgiOutput1 = nullptr;
        hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
        SAFE_RELEASE(pDxgiOutput);
        if (FAILED(hr)) return false;
        
        hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
        SAFE_RELEASE(pDxgiOutput1);
        if (FAILED(hr)) return false;
        
        return true;
    }

    bool CreateStagingTexture() {
        if (m_pStagingTexture) {
            return true;
        }
        
        DXGI_OUTDUPL_DESC duplDesc;
        m_pDeskDupl->GetDesc(&duplDesc);
        
        D3D11_TEXTURE2D_DESC desc = {};
        desc.Width = duplDesc.ModeDesc.Width;
        desc.Height = duplDesc.ModeDesc.Height;
        desc.MipLevels = 1;
        desc.ArraySize = 1;
        desc.Format = duplDesc.ModeDesc.Format;
        desc.SampleDesc.Count = 1;
        desc.SampleDesc.Quality = 0;
        desc.Usage = D3D11_USAGE_STAGING;
        desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        desc.BindFlags = 0;
        desc.MiscFlags = 0;
        
        HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
        return SUCCEEDED(hr);
    }

    HRESULT GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height) {
        if (!m_pDeskDupl) {
            return E_FAIL;
        }

        HRESULT hr;
        IDXGIResource* pDesktopResource = nullptr;
        DXGI_OUTDUPL_FRAME_INFO frameInfo;

        // 获取下一帧
        hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);
        
        if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
            return S_FALSE; // 没有新帧
        }
        if (FAILED(hr)) {
            return hr;
        }

        // 转换为纹理
        ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
        hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
        SAFE_RELEASE(pDesktopResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 创建中转纹理
        if (!CreateStagingTexture()) {
            SAFE_RELEASE(pAcquiredDesktopImage);
            m_pDeskDupl->ReleaseFrame();
            return E_FAIL;
        }

        // 复制到中转纹理
        m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
        SAFE_RELEASE(pAcquiredDesktopImage);

        // 映射纹理数据
        D3D11_MAPPED_SUBRESOURCE mappedResource;
        hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 高效内存拷贝
        const UINT rowPitch = mappedResource.RowPitch;
        const UINT framePitch = m_screenWidth * 4; // BGRA
        frameBuffer.resize(framePitch * m_screenHeight);
        
        BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
        BYTE* pDest = frameBuffer.data();
        
        if (rowPitch == framePitch) {
            // 一次性拷贝
            memcpy(pDest, pSource, framePitch * m_screenHeight);
        } else {
            // 逐行拷贝
            for (int y = 0; y < m_screenHeight; ++y) {
                memcpy(pDest, pSource, framePitch);
                pSource += rowPitch;
                pDest += framePitch;
            }
        }

        // 清理
        m_pContext->Unmap(m_pStagingTexture, 0);
        m_pDeskDupl->ReleaseFrame();

        width = m_screenWidth;
        height = m_screenHeight;

        return S_OK;
    }
};

int main(int argc, char* argv[]) {
    try {
        std::cout << "=== Screen Capture with ONNX Inference ===" << std::endl;
        std::cout << "Program started successfully" << std::endl;

        // 检查命令行参数
        std::string modelPath;
        if (argc > 1) {
            modelPath = argv[1];
            std::cout << "Model path: " << modelPath << std::endl;
        } else {
            std::cout << "No model path provided. Running in capture-only mode." << std::endl;
            std::cout << "Usage: " << argv[0] << " [model_path.onnx]" << std::endl;
        }

        std::cout << "Creating capture object..." << std::endl;

        CaptureWithInference capture(320);
        std::cout << "Initializing capture..." << std::endl;

        if (!capture.Initialize(modelPath)) {
            std::cerr << "Failed to initialize capture" << std::endl;
            return -1;
        }

        std::cout << "Capture initialized successfully" << std::endl;

    // 创建显示窗口
    cv::namedWindow("Capture + Inference", cv::WINDOW_NORMAL);
    cv::resizeWindow("Capture + Inference", 320, 320);

    std::cout << "Starting capture and inference..." << std::endl;
    std::cout << "Press 'q' to quit, 's' to save screenshot" << std::endl;

    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    auto lastFpsTime = startTime;
    double currentFps = 0.0;

    while (true) {
        cv::Mat frame = capture.CaptureROI();

        if (frame.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        frameCount++;

        // 执行推理（如果有模型）
        std::vector<float> inferenceResult;
        double inferenceTime = 0.0;

        if (capture.HasInference()) {
            auto inferenceStart = std::chrono::high_resolution_clock::now();
            inferenceResult = capture.CaptureAndInference();
            auto inferenceEnd = std::chrono::high_resolution_clock::now();
            inferenceTime = std::chrono::duration<double, std::milli>(inferenceEnd - inferenceStart).count();
        }

        // 计算并更新FPS
        auto currentTime = std::chrono::high_resolution_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastFpsTime).count() >= 1000) {
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            currentFps = frameCount * 1000.0 / duration.count();

            if (capture.HasInference()) {
                auto inference = capture.GetInference();
                std::cout << "Frame " << frameCount
                          << " | FPS: " << currentFps
                          << " | Inference: " << inference->GetLastInferenceTime() << "ms"
                          << " | Avg: " << inference->GetAverageInferenceTime() << "ms"
                          << " | Provider: " << inference->GetExecutionProvider() << std::endl;
            } else {
                std::cout << "Frame " << frameCount << " | FPS: " << currentFps << std::endl;
            }

            lastFpsTime = currentTime;
        }

        // 转换为BGR用于显示
        cv::Mat displayFrame;
        cv::cvtColor(frame, displayFrame, cv::COLOR_RGB2BGR);

        // 在左上角显示信息
        std::string fpsText = "FPS: " + std::to_string((int)currentFps);
        cv::putText(displayFrame, fpsText, cv::Point(5, 20), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 1);

        if (capture.HasInference()) {
            auto inference = capture.GetInference();
            std::string inferenceText = "Inf: " + std::to_string((int)inference->GetLastInferenceTime()) + "ms";
            std::string providerText = inference->GetExecutionProvider();
            cv::putText(displayFrame, inferenceText, cv::Point(5, 40), cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(255, 255, 0), 1);
            cv::putText(displayFrame, providerText, cv::Point(5, 60), cv::FONT_HERSHEY_SIMPLEX, 0.4, cv::Scalar(0, 255, 255), 1);

            // 显示推理结果的前几个值
            if (!inferenceResult.empty()) {
                std::string resultText = "Out: ";
                for (size_t i = 0; i < std::min(size_t(3), inferenceResult.size()); ++i) {
                    resultText += std::to_string(inferenceResult[i]).substr(0, 5) + " ";
                }
                cv::putText(displayFrame, resultText, cv::Point(5, 80), cv::FONT_HERSHEY_SIMPLEX, 0.3, cv::Scalar(255, 0, 255), 1);
            }
        }

        // 绘制中心十字线
        int centerX = displayFrame.cols / 2;
        int centerY = displayFrame.rows / 2;
        cv::line(displayFrame, cv::Point(centerX - 10, centerY), cv::Point(centerX + 10, centerY), cv::Scalar(0, 0, 255), 1);
        cv::line(displayFrame, cv::Point(centerX, centerY - 10), cv::Point(centerX, centerY + 10), cv::Scalar(0, 0, 255), 1);

        cv::imshow("Capture + Inference", displayFrame);

        // 处理按键
        int key = cv::waitKey(1) & 0xFF;
        if (key == 'q' || key == 27) { // 'q' 或 ESC
            break;
        } else if (key == 's') { // 保存截图
            std::string filename = "capture_inference_" + std::to_string(frameCount) + ".jpg";
            cv::imwrite(filename, displayFrame);
            std::cout << "Screenshot saved: " << filename << std::endl;
        }
    }

    // 最终统计
    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    double avgFps = frameCount * 1000.0 / totalDuration.count();

    std::cout << "\n=== Final Statistics ===" << std::endl;
    std::cout << "Total frames: " << frameCount << std::endl;
    std::cout << "Runtime: " << totalDuration.count() << " ms" << std::endl;
    std::cout << "Average FPS: " << avgFps << std::endl;

    if (capture.HasInference()) {
        auto inference = capture.GetInference();
        std::cout << "Total inferences: " << inference->GetInferenceCount() << std::endl;
        std::cout << "Average inference time: " << inference->GetAverageInferenceTime() << " ms" << std::endl;
        std::cout << "Execution provider: " << inference->GetExecutionProvider() << std::endl;
    }

        cv::destroyAllWindows();
        std::cout << "Program completed!" << std::endl;

        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "Unknown exception caught" << std::endl;
        return -1;
    }
}
