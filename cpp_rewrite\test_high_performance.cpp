#include "include/HighPerformanceCapture.h"
#include <iostream>
#include <chrono>
#include <thread>

void TestSyncCapture() {
    std::cout << "\n=== Synchronous Capture Test ===" << std::endl;
    
    HighPerformanceCapture::Config config;
    config.roiWidth = 640;
    config.roiHeight = 640;
    config.targetWidth = 320;
    config.targetHeight = 320;
    config.enableAsync = false;
    
    auto capture = HighPerformanceCaptureFactory::CreateCapture(config);
    if (!capture) {
        std::cerr << "Failed to create capture" << std::endl;
        return;
    }
    
    std::cout << "Testing synchronous capture for 5 seconds..." << std::endl;
    
    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    auto endTime = startTime + std::chrono::seconds(5);
    
    while (std::chrono::high_resolution_clock::now() < endTime) {
        cv::Mat frame = capture->CaptureFrame();
        
        if (!frame.empty()) {
            frameCount++;
            
            // 每1000帧显示一次统计
            if (frameCount % 1000 == 0) {
                auto stats = capture->GetPerformanceStats();
                std::cout << "Frame " << frameCount 
                          << " | FPS: " << stats.GetFPS()
                          << " | Capture: " << stats.avgCaptureTime.load() << "ms"
                          << " | Process: " << stats.avgProcessTime.load() << "ms" << std::endl;
            }
        }
    }
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);
    double avgFps = frameCount * 1000.0 / duration.count();
    
    auto finalStats = capture->GetPerformanceStats();
    std::cout << "\n=== Sync Results ===" << std::endl;
    std::cout << "Total frames: " << frameCount << std::endl;
    std::cout << "Average FPS: " << avgFps << std::endl;
    std::cout << "Avg capture time: " << finalStats.avgCaptureTime.load() << " ms" << std::endl;
    std::cout << "Avg process time: " << finalStats.avgProcessTime.load() << " ms" << std::endl;
}

void TestAsyncCapture() {
    std::cout << "\n=== Asynchronous Capture Test ===" << std::endl;
    
    HighPerformanceCapture::Config config;
    config.roiWidth = 640;
    config.roiHeight = 640;
    config.targetWidth = 320;
    config.targetHeight = 320;
    config.enableAsync = true;
    config.bufferPoolSize = 20;
    config.maxQueueSize = 5;
    
    auto capture = HighPerformanceCaptureFactory::CreateCapture(config);
    if (!capture) {
        std::cerr << "Failed to create capture" << std::endl;
        return;
    }
    
    // 启动异步捕获
    if (!capture->StartAsync()) {
        std::cerr << "Failed to start async capture" << std::endl;
        return;
    }
    
    std::cout << "Testing asynchronous capture for 5 seconds..." << std::endl;
    
    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    auto endTime = startTime + std::chrono::seconds(5);
    
    while (std::chrono::high_resolution_clock::now() < endTime) {
        cv::Mat frame = capture->GetProcessedFrame(1); // 1ms timeout
        
        if (!frame.empty()) {
            frameCount++;
            
            // 每1000帧显示一次统计
            if (frameCount % 1000 == 0) {
                auto stats = capture->GetPerformanceStats();
                std::cout << "Frame " << frameCount 
                          << " | FPS: " << stats.GetFPS()
                          << " | Capture: " << stats.avgCaptureTime.load() << "ms"
                          << " | Process: " << stats.avgProcessTime.load() << "ms" << std::endl;
            }
        }
    }
    
    capture->StopAsync();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);
    double avgFps = frameCount * 1000.0 / duration.count();
    
    auto finalStats = capture->GetPerformanceStats();
    std::cout << "\n=== Async Results ===" << std::endl;
    std::cout << "Total frames: " << frameCount << std::endl;
    std::cout << "Average FPS: " << avgFps << std::endl;
    std::cout << "Avg capture time: " << finalStats.avgCaptureTime.load() << " ms" << std::endl;
    std::cout << "Avg process time: " << finalStats.avgProcessTime.load() << " ms" << std::endl;
}

void TestWithDisplay() {
    std::cout << "\n=== Display Test ===" << std::endl;
    
    HighPerformanceCapture::Config config;
    config.roiWidth = 640;
    config.roiHeight = 640;
    config.targetWidth = 320;
    config.targetHeight = 320;
    config.enableAsync = true;
    
    auto capture = HighPerformanceCaptureFactory::CreateCapture(config);
    if (!capture) {
        std::cerr << "Failed to create capture" << std::endl;
        return;
    }
    
    capture->StartAsync();
    
    // 创建显示窗口
    cv::namedWindow("High Performance Capture", cv::WINDOW_NORMAL);
    cv::resizeWindow("High Performance Capture", 640, 640);
    
    std::cout << "Displaying frames. Press 'q' to quit, 's' to save." << std::endl;
    
    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    auto lastStatsTime = startTime;
    
    while (true) {
        cv::Mat frame = capture->GetProcessedFrame(5);
        
        if (!frame.empty()) {
            frameCount++;
            
            // 放大显示
            cv::Mat displayFrame;
            cv::resize(frame, displayFrame, cv::Size(640, 640), 0, 0, cv::INTER_NEAREST);
            
            // 转换为BGR用于显示
            cv::Mat bgrFrame;
            cv::cvtColor(displayFrame, bgrFrame, cv::COLOR_RGB2BGR);
            
            // 显示FPS信息
            auto currentTime = std::chrono::high_resolution_clock::now();
            if (std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastStatsTime).count() >= 1000) {
                auto stats = capture->GetPerformanceStats();
                std::string fpsText = "FPS: " + std::to_string((int)stats.GetFPS());
                std::string captureText = "Capture: " + std::to_string(stats.avgCaptureTime.load()) + "ms";
                std::string processText = "Process: " + std::to_string(stats.avgProcessTime.load()) + "ms";
                
                cv::putText(bgrFrame, fpsText, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);
                cv::putText(bgrFrame, captureText, cv::Point(10, 70), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
                cv::putText(bgrFrame, processText, cv::Point(10, 110), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
                
                lastStatsTime = currentTime;
            }
            
            cv::imshow("High Performance Capture", bgrFrame);
        }
        
        // 处理按键
        int key = cv::waitKey(1) & 0xFF;
        if (key == 'q' || key == 27) {
            break;
        } else if (key == 's') {
            if (!frame.empty()) {
                cv::Mat saveFrame;
                cv::cvtColor(frame, saveFrame, cv::COLOR_RGB2BGR);
                std::string filename = "hp_capture_" + std::to_string(frameCount) + ".jpg";
                cv::imwrite(filename, saveFrame);
                std::cout << "Saved: " << filename << std::endl;
            }
        }
    }
    
    capture->StopAsync();
    cv::destroyAllWindows();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);
    double avgFps = frameCount * 1000.0 / duration.count();
    
    std::cout << "\n=== Display Test Results ===" << std::endl;
    std::cout << "Total frames displayed: " << frameCount << std::endl;
    std::cout << "Display FPS: " << avgFps << std::endl;
}

int main() {
    std::cout << "=== High Performance Capture Test Suite ===" << std::endl;
    
    // 检查系统能力
    if (!HighPerformanceCaptureFactory::TestSystemCapabilities()) {
        std::cerr << "System does not support required capabilities" << std::endl;
        return -1;
    }
    
    std::cout << "System capabilities check passed" << std::endl;
    
    // 运行测试
    TestSyncCapture();
    TestAsyncCapture();
    TestWithDisplay();
    
    std::cout << "\n=== All Tests Completed ===" << std::endl;
    return 0;
}
