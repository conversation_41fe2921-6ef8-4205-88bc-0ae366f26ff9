{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3]}, {"build": "pybind11", "jsonFile": "directory-pybind11-Debug-509bf82f3abcddd3d4c7.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 1, "source": "pybind11", "targetIndexes": [1]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "aim_controller", "targetIndexes": [0, 2, 3]}, {"directoryIndexes": [1], "name": "pybind11", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-c3d8ae2c351d1d0dbce8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@ea61fbb5e2027680e1c7", "jsonFile": "target-ALL_BUILD-Debug-7cf830b3120b63e4113a.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-b49a7245a42e04adb7ef.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aim_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-aim_controller-Debug-efb9252911143c0bec9a.json", "name": "aim_controller", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3]}, {"build": "pybind11", "jsonFile": "directory-pybind11-Release-509bf82f3abcddd3d4c7.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 1, "source": "pybind11", "targetIndexes": [1]}], "name": "Release", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "aim_controller", "targetIndexes": [0, 2, 3]}, {"directoryIndexes": [1], "name": "pybind11", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-c3d8ae2c351d1d0dbce8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@ea61fbb5e2027680e1c7", "jsonFile": "target-ALL_BUILD-Release-7cf830b3120b63e4113a.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-b49a7245a42e04adb7ef.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aim_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-aim_controller-Release-a57adf9e39d20fba5bf2.json", "name": "aim_controller", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3]}, {"build": "pybind11", "jsonFile": "directory-pybind11-MinSizeRel-509bf82f3abcddd3d4c7.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 1, "source": "pybind11", "targetIndexes": [1]}], "name": "MinSizeRel", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "aim_controller", "targetIndexes": [0, 2, 3]}, {"directoryIndexes": [1], "name": "pybind11", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-c3d8ae2c351d1d0dbce8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@ea61fbb5e2027680e1c7", "jsonFile": "target-ALL_BUILD-MinSizeRel-7cf830b3120b63e4113a.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-b49a7245a42e04adb7ef.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aim_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-aim_controller-MinSizeRel-665e6334b5c9c6ca98cf.json", "name": "aim_controller", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3]}, {"build": "pybind11", "jsonFile": "directory-pybind11-RelWithDebInfo-509bf82f3abcddd3d4c7.json", "minimumCMakeVersion": {"string": "3.15"}, "parentIndex": 0, "projectIndex": 1, "source": "pybind11", "targetIndexes": [1]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "aim_controller", "targetIndexes": [0, 2, 3]}, {"directoryIndexes": [1], "name": "pybind11", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-c3d8ae2c351d1d0dbce8.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@ea61fbb5e2027680e1c7", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-7cf830b3120b63e4113a.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-b49a7245a42e04adb7ef.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "aim_controller::@6890427a1f51a3e7e1df", "jsonFile": "target-aim_controller-RelWithDebInfo-d0bee13341b83667f600.json", "name": "aim_controller", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/foye_111111/build", "source": "C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller"}, "version": {"major": 2, "minor": 4}}