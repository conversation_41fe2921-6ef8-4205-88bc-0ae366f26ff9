#include "../include/PostProcessor.h"

PostProcessor::PostProcessor(float confidenceThreshold, float nmsThreshold, int numClasses)
    : m_confidenceThreshold(confidenceThreshold)
    , m_nmsThreshold(nmsThreshold)
    , m_numClasses(numClasses)
{
    memset(&m_stats, 0, sizeof(m_stats));
}

std::vector<Detection> PostProcessor::ProcessOutput(
    const std::vector<float>& output,
    const cv::Size& inputSize,
    const cv::Size& originalSize
) {
    if (output.empty()) {
        return {};
    }

    StartTimer();

    // 解析YOLO输出
    std::vector<Detection> detections = ParseYOLOv8Output(output, inputSize);
    
    // 应用NMS
    detections = ApplyNMS(detections);
    
    // 缩放到原始尺寸
    detections = ScaleDetections(detections, inputSize, originalSize);

    double processTime = EndTimer();
    UpdatePerformanceStats(processTime);

    return detections;
}

std::shared_ptr<Detection> PostProcessor::SelectBestTarget(
    const std::vector<Detection>& detections,
    int aimPriority,
    const cv::Point2f& crosshair,
    float aimRadius
) {
    if (detections.empty()) {
        return nullptr;
    }

    std::shared_ptr<Detection> bestTarget = nullptr;
    float bestScore = -1.0f;

    for (const auto& detection : detections) {
        // 检查是否在瞄准圆内
        float distance = Utils::Distance(detection.center, crosshair);
        if (distance > aimRadius) {
            continue;
        }

        // 根据优先级过滤
        if (aimPriority == 1 && detection.classId != 0) continue; // 头部优先
        if (aimPriority == 2 && detection.classId != 1) continue; // 身体优先

        // 计算综合得分 (置信度 + 距离权重)
        float distanceWeight = 1.0f - (distance / aimRadius);
        float score = detection.confidence * 0.7f + distanceWeight * 0.3f;

        if (score > bestScore) {
            bestScore = score;
            bestTarget = std::make_shared<Detection>(detection);
        }
    }

    return bestTarget;
}

cv::Mat PostProcessor::DrawDetections(
    const cv::Mat& image,
    const std::vector<Detection>& detections,
    const std::shared_ptr<Detection>& bestTarget,
    const cv::Point2f& crosshair,
    float aimRadius
) {
    cv::Mat result = image.clone();

    // 绘制瞄准圆
    cv::circle(result, cv::Point(static_cast<int>(crosshair.x), static_cast<int>(crosshair.y)),
               static_cast<int>(aimRadius), cv::Scalar(255, 255, 255), 2);
    
    // 绘制准星
    int crossSize = 10;
    int cx = static_cast<int>(crosshair.x);
    int cy = static_cast<int>(crosshair.y);
    cv::line(result,
             cv::Point(cx - crossSize, cy),
             cv::Point(cx + crossSize, cy),
             cv::Scalar(0, 255, 0), 2);
    cv::line(result,
             cv::Point(cx, cy - crossSize),
             cv::Point(cx, cy + crossSize),
             cv::Scalar(0, 255, 0), 2);

    // 绘制所有检测结果
    for (const auto& detection : detections) {
        cv::Scalar color = (detection.classId == 0) ? cv::Scalar(0, 255, 255) : cv::Scalar(255, 0, 255); // 头部黄色，身体紫色
        
        // 绘制边界框
        cv::rectangle(result, detection.box, color, 2);
        
        // 绘制置信度
        std::string label = std::to_string((int)(detection.confidence * 100)) + "%";
        cv::putText(result, label, cv::Point(detection.box.x, detection.box.y - 5), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.5, color, 1);
        
        // 绘制中心点
        cv::circle(result, detection.center, 3, color, -1);
    }

    // 高亮最佳目标
    if (bestTarget) {
        cv::rectangle(result, bestTarget->box, cv::Scalar(0, 0, 255), 3); // 红色高亮
        cv::circle(result, bestTarget->center, 5, cv::Scalar(0, 0, 255), -1);
        
        // 绘制瞄准线
        cv::line(result, crosshair, bestTarget->center, cv::Scalar(0, 0, 255), 2);
    }

    return result;
}

std::vector<Detection> PostProcessor::ParseYOLOv8Output(
    const std::vector<float>& output,
    const cv::Size& inputSize
) {
    std::vector<Detection> detections;
    
    // YOLOv8输出格式: [1, 84, 8400] 或类似
    // 84 = 4 (bbox) + 80 (classes) 对于COCO，或者 4 + num_classes
    int numDetections = output.size() / (4 + m_numClasses);
    int stride = 4 + m_numClasses;

    for (int i = 0; i < numDetections; ++i) {
        int offset = i * stride;
        
        // 获取边界框 (中心点格式)
        float cx = output[offset + 0];
        float cy = output[offset + 1];
        float w = output[offset + 2];
        float h = output[offset + 3];
        
        // 转换为左上角格式
        float x = cx - w * 0.5f;
        float y = cy - h * 0.5f;
        
        // 获取最高置信度的类别
        float maxConf = 0.0f;
        int maxClass = -1;
        
        for (int c = 0; c < m_numClasses; ++c) {
            float conf = output[offset + 4 + c];
            if (conf > maxConf) {
                maxConf = conf;
                maxClass = c;
            }
        }
        
        // 过滤低置信度检测
        if (maxConf > m_confidenceThreshold) {
            cv::Rect2f box(x, y, w, h);
            detections.emplace_back(box, maxConf, maxClass);
        }
    }

    return detections;
}

std::vector<Detection> PostProcessor::ApplyNMS(const std::vector<Detection>& detections) {
    if (detections.empty()) {
        return {};
    }

    // 按置信度排序
    std::vector<Detection> sortedDetections = detections;
    std::sort(sortedDetections.begin(), sortedDetections.end(),
              [](const Detection& a, const Detection& b) {
                  return a.confidence > b.confidence;
              });

    std::vector<bool> suppressed(sortedDetections.size(), false);
    std::vector<Detection> result;

    for (size_t i = 0; i < sortedDetections.size(); ++i) {
        if (suppressed[i]) continue;

        result.push_back(sortedDetections[i]);

        // 抑制重叠的检测
        for (size_t j = i + 1; j < sortedDetections.size(); ++j) {
            if (suppressed[j]) continue;

            float iou = CalculateIoU(sortedDetections[i].box, sortedDetections[j].box);
            if (iou > m_nmsThreshold) {
                suppressed[j] = true;
            }
        }
    }

    return result;
}

std::vector<Detection> PostProcessor::ScaleDetections(
    const std::vector<Detection>& detections,
    const cv::Size& inputSize,
    const cv::Size& originalSize
) {
    if (inputSize == originalSize) {
        return detections;
    }

    float scaleX = static_cast<float>(originalSize.width) / inputSize.width;
    float scaleY = static_cast<float>(originalSize.height) / inputSize.height;

    std::vector<Detection> scaledDetections;
    scaledDetections.reserve(detections.size());

    for (const auto& detection : detections) {
        cv::Rect2f scaledBox;
        scaledBox.x = detection.box.x * scaleX;
        scaledBox.y = detection.box.y * scaleY;
        scaledBox.width = detection.box.width * scaleX;
        scaledBox.height = detection.box.height * scaleY;

        scaledDetections.emplace_back(scaledBox, detection.confidence, detection.classId);
    }

    return scaledDetections;
}

float PostProcessor::CalculateIoU(const cv::Rect2f& box1, const cv::Rect2f& box2) {
    float intersectionArea = (box1 & box2).area();
    float unionArea = box1.area() + box2.area() - intersectionArea;
    
    return (unionArea > 0) ? (intersectionArea / unionArea) : 0.0f;
}

void PostProcessor::StartTimer() const {
    m_startTime = Utils::GetCurrentTime();
}

double PostProcessor::EndTimer() const {
    return Utils::GetElapsedTime(m_startTime);
}

void PostProcessor::UpdatePerformanceStats(double processTime) const {
    m_stats.postprocessTimeMs = processTime;
    m_stats.frameCount++;
}
