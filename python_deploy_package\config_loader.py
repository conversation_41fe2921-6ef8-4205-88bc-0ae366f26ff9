# -*- coding: utf-8 -*-
"""
配置文件加载器
支持从外部JSON文件加载配置，方便用户自定义参数
"""

import json
import os
import sys
from typing import Dict, Any

class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置加载器
        
        参数:
            config_file: 配置文件路径，默认为 config.json
        """
        self.config_file = config_file
        self.config = {}
        self.default_config = self._get_default_config()
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置（作为备用）"""
        return {
            "model": {
                "path": "models/PUBGV8_320.onnx",
                "confidence_threshold": 0.65,
                "nms_threshold": 0.2,
                "model_type": "yolov8",
                "num_classes": 0,
                "class_names": []
            },
            "capture": {
                "method": "screen",
                "screen_resolution": [1920, 1080],
                "roi_size": 640,
                "udp": {
                    "host": "127.0.0.1",
                    "port": 8000,
                    "buffer_size": 65536
                }
            },
            "mouse": {
                "move_mode": "kmbox",
                "kmbox": {
                    "ip": "*************",
                    "port": "8808",
                    "mac": "62587019",
                    "move_type": "normal"
                },
                "aim_keys": {
                    "priority_1": [
                        {"key": 0x05, "offset_y": 0.3, "trigger": {"enabled": True, "fire_delay_ms": 30, "fire_interval_ms": 100}}
                    ],
                    "priority_2": [
                        {"key": 0x01, "offset_y": 0.4, "trigger": {"enabled": False}},
                        {"key": 0x06, "offset_y": 0.3, "trigger": {"enabled": True, "fire_delay_ms": 50, "fire_interval_ms": 120}}
                    ]
                }
            },
            "aim_circle_radius": 90,
            "pid": {
                "system": {
                    "kp": 50.0, "ki": 0.0, "kd": 0.005,
                    "base_ki": 0.0, "max_ki": 80.0,
                    "inner_threshold": 20.0, "outer_threshold": 50.0
                },
                "kmbox": {
                    "kp": 50.0, "ki": 0.0, "kd": 0.05,
                    "base_ki": 0.0, "max_ki": 80.0,
                    "inner_threshold": 50.0, "outer_threshold": 100.0
                },
                "ghub": {
                    "kp": 50.0, "ki": 0.0, "kd": 0.005,
                    "base_ki": 0.0, "max_ki": 80.0,
                    "inner_threshold": 20.0, "outer_threshold": 50.0
                },
                "control_frequency": 500
            },
            "display": {
                "font_path": "C:/Windows/Fonts/simhei.ttf",
                "font_size": 20,
                "show_preview": True
            },
            "debug": {
                "show_timing": False
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        返回:
            配置字典
        """
        # 首先使用默认配置
        self.config = self.default_config.copy()
        
        # 尝试加载外部配置文件
        if os.path.exists(self.config_file):
            try:
                print(f"[配置] 正在加载配置文件: {self.config_file}")
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    external_config = json.load(f)
                
                # 递归合并配置
                self._merge_config(self.config, external_config)
                print(f"[配置] 配置文件加载成功!")
                
            except json.JSONDecodeError as e:
                print(f"[配置] 配置文件JSON格式错误: {e}")
                print(f"[配置] 使用默认配置")
            except Exception as e:
                print(f"[配置] 加载配置文件失败: {e}")
                print(f"[配置] 使用默认配置")
        else:
            print(f"[配置] 配置文件 {self.config_file} 不存在，使用默认配置")
            # 创建默认配置文件
            self.save_config()
        
        return self.config
    
    def _merge_config(self, base_config: Dict[str, Any], new_config: Dict[str, Any]):
        """
        递归合并配置字典
        
        参数:
            base_config: 基础配置字典
            new_config: 新配置字典
        """
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                self._merge_config(base_config[key], value)
            else:
                # 直接覆盖或添加新键
                base_config[key] = value
    
    def save_config(self):
        """保存当前配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            print(f"[配置] 配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"[配置] 保存配置文件失败: {e}")
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config
    
    def update_config(self, updates: Dict[str, Any]):
        """
        更新配置
        
        参数:
            updates: 要更新的配置字典
        """
        self._merge_config(self.config, updates)
    
    def print_config_summary(self):
        """打印配置摘要"""
        print(f"\n--- 配置摘要 ---")
        print(f"模型路径: {self.config['model']['path']}")
        print(f"模型类型: {self.config['model']['model_type'].upper()}")
        print(f"置信度阈值: {self.config['model']['confidence_threshold']}")
        print(f"NMS阈值: {self.config['model']['nms_threshold']}")
        print(f"截图方式: {self.config['capture']['method']}")
        print(f"移动方式: {self.config['mouse']['move_mode']}")
        print(f"控制频率: {self.config['pid']['control_frequency']}Hz")
        print(f"自瞄圆环半径: {self.config['aim_circle_radius']}像素")
        
        # 显示扳机配置
        print(f"\n--- 扳机配置 ---")
        for prio_name, keys in self.config['mouse']['aim_keys'].items():
            print(f"{prio_name}:")
            for i, key_cfg in enumerate(keys):
                trigger = key_cfg.get('trigger', {})
                enabled = trigger.get('enabled', False)
                if enabled:
                    delay = trigger.get('fire_delay_ms', 0)
                    interval = trigger.get('fire_interval_ms', 0)
                    print(f"  按键{key_cfg['key']}: 扳机启用 (延迟{delay}ms, 间隔{interval}ms)")
                else:
                    print(f"  按键{key_cfg['key']}: 扳机禁用")

# 全局配置加载器实例
config_loader = ConfigLoader()

def load_config(config_file: str = "config.json") -> Dict[str, Any]:
    """
    便捷函数：加载配置
    
    参数:
        config_file: 配置文件路径
        
    返回:
        配置字典
    """
    global config_loader
    config_loader = ConfigLoader(config_file)
    return config_loader.load_config()

def get_config() -> Dict[str, Any]:
    """获取当前配置"""
    return config_loader.get_config()

def update_config(updates: Dict[str, Any]):
    """更新配置"""
    config_loader.update_config(updates)

def save_config():
    """保存配置"""
    config_loader.save_config()

def print_config_summary():
    """打印配置摘要"""
    config_loader.print_config_summary()
