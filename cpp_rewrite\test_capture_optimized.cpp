#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>

#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

class OptimizedScreenCapture {
private:
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    int m_screenWidth;
    int m_screenHeight;
    bool m_initialized;
    
    // 性能统计
    int m_frameCount;
    std::chrono::high_resolution_clock::time_point m_startTime;
    std::chrono::high_resolution_clock::time_point m_lastFrameTime;

public:
    OptimizedScreenCapture() 
        : m_pDevice(nullptr)
        , m_pContext(nullptr)
        , m_pDeskDupl(nullptr)
        , m_pStagingTexture(nullptr)
        , m_screenWidth(0)
        , m_screenHeight(0)
        , m_initialized(false)
        , m_frameCount(0)
    {
        m_startTime = std::chrono::high_resolution_clock::now();
        m_lastFrameTime = m_startTime;
    }

    ~OptimizedScreenCapture() {
        Release();
    }

    bool Initialize() {
        if (m_initialized) {
            return true;
        }

        m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
        m_screenHeight = GetSystemMetrics(SM_CYSCREEN);
        
        std::cout << "Screen size: " << m_screenWidth << "x" << m_screenHeight << std::endl;

        if (!InitializeDXGI()) {
            std::cerr << "Failed to initialize DXGI" << std::endl;
            return false;
        }

        m_initialized = true;
        std::cout << "Optimized screen capture initialized successfully" << std::endl;
        return true;
    }

    // 纯性能测试 - 不做任何显示和处理
    bool CaptureRawPerformanceTest() {
        if (!m_initialized) {
            return false;
        }

        HRESULT hr;
        IDXGIResource* pDesktopResource = nullptr;
        DXGI_OUTDUPL_FRAME_INFO frameInfo;

        // 获取下一帧 - 不等待
        hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);
        
        if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
            return false; // 没有新帧
        }
        if (FAILED(hr)) {
            return false;
        }

        // 立即释放，不做任何处理
        SAFE_RELEASE(pDesktopResource);
        m_pDeskDupl->ReleaseFrame();

        m_frameCount++;
        
        // 每1000帧统计一次性能
        if (m_frameCount % 1000 == 0) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(currentTime - m_startTime);
            double totalSeconds = duration.count() / 1000000.0;
            double avgFps = m_frameCount / totalSeconds;
            
            // 计算瞬时FPS
            auto frameDuration = std::chrono::duration_cast<std::chrono::microseconds>(currentTime - m_lastFrameTime);
            double instantFps = 1000.0 / (frameDuration.count() / 1000.0); // 最近1000帧的平均FPS
            
            std::cout << "Frame " << m_frameCount 
                      << " | Avg FPS: " << avgFps 
                      << " | Instant FPS: " << instantFps << std::endl;
            
            m_lastFrameTime = currentTime;
        }

        return true;
    }

    // 带最小处理的测试
    bool CaptureWithMinimalProcessing() {
        if (!m_initialized) {
            return false;
        }

        std::vector<BYTE> frameBuffer;
        int width, height;
        
        HRESULT hr = GetNextFrame(frameBuffer, width, height);
        if (FAILED(hr) || hr == S_FALSE) {
            return false;
        }

        m_frameCount++;
        
        // 每1000帧统计一次
        if (m_frameCount % 1000 == 0) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(currentTime - m_startTime);
            double totalSeconds = duration.count() / 1000000.0;
            double avgFps = m_frameCount / totalSeconds;
            
            std::cout << "Frame " << m_frameCount << " | FPS: " << avgFps << std::endl;
        }

        return true;
    }

    void Release() {
        SAFE_RELEASE(m_pStagingTexture);
        SAFE_RELEASE(m_pDeskDupl);
        SAFE_RELEASE(m_pContext);
        SAFE_RELEASE(m_pDevice);
        
        m_initialized = false;
    }

private:
    bool InitializeDXGI() {
        HRESULT hr = S_OK;

        D3D_FEATURE_LEVEL featureLevel;
        hr = D3D11CreateDevice(
            nullptr,
            D3D_DRIVER_TYPE_HARDWARE,
            nullptr,
            0, // 不使用调试标志
            nullptr,
            0,
            D3D11_SDK_VERSION,
            &m_pDevice,
            &featureLevel,
            &m_pContext
        );

        if (FAILED(hr)) {
            std::cerr << "Failed to create D3D11 device: 0x" << std::hex << hr << std::endl;
            return false;
        }

        IDXGIDevice* pDxgiDevice = nullptr;
        hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
        if (FAILED(hr)) {
            return false;
        }

        IDXGIAdapter* pDxgiAdapter = nullptr;
        hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
        SAFE_RELEASE(pDxgiDevice);
        if (FAILED(hr)) {
            return false;
        }

        IDXGIOutput* pDxgiOutput = nullptr;
        hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
        SAFE_RELEASE(pDxgiAdapter);
        if (FAILED(hr)) {
            return false;
        }

        IDXGIOutput1* pDxgiOutput1 = nullptr;
        hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
        SAFE_RELEASE(pDxgiOutput);
        if (FAILED(hr)) {
            return false;
        }

        hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
        SAFE_RELEASE(pDxgiOutput1);
        if (FAILED(hr)) {
            std::cerr << "Failed to duplicate output: 0x" << std::hex << hr << std::endl;
            return false;
        }

        return true;
    }

    bool CreateStagingTexture() {
        DXGI_OUTDUPL_DESC duplDesc;
        m_pDeskDupl->GetDesc(&duplDesc);

        D3D11_TEXTURE2D_DESC desc = {};
        desc.Width = duplDesc.ModeDesc.Width;
        desc.Height = duplDesc.ModeDesc.Height;
        desc.MipLevels = 1;
        desc.ArraySize = 1;
        desc.Format = duplDesc.ModeDesc.Format;
        desc.SampleDesc.Count = 1;
        desc.SampleDesc.Quality = 0;
        desc.Usage = D3D11_USAGE_STAGING;
        desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        desc.BindFlags = 0;
        desc.MiscFlags = 0;

        HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
        return SUCCEEDED(hr);
    }

    HRESULT GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height) {
        if (!m_pDeskDupl) {
            return E_FAIL;
        }

        HRESULT hr;
        IDXGIResource* pDesktopResource = nullptr;
        DXGI_OUTDUPL_FRAME_INFO frameInfo;

        hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);
        
        if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
            return S_FALSE;
        }
        if (FAILED(hr)) {
            return hr;
        }

        ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
        hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
        SAFE_RELEASE(pDesktopResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        if (!m_pStagingTexture) {
            if (!CreateStagingTexture()) {
                SAFE_RELEASE(pAcquiredDesktopImage);
                m_pDeskDupl->ReleaseFrame();
                return E_FAIL;
            }
        }

        m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
        SAFE_RELEASE(pAcquiredDesktopImage);

        D3D11_MAPPED_SUBRESOURCE mappedResource;
        hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 快速内存拷贝
        const UINT rowPitch = mappedResource.RowPitch;
        const UINT framePitch = m_screenWidth * 4;
        frameBuffer.resize(framePitch * m_screenHeight);

        BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
        BYTE* pDest = frameBuffer.data();
        
        // 优化的内存拷贝
        if (rowPitch == framePitch) {
            // 如果行间距相同，可以一次性拷贝
            memcpy(pDest, pSource, framePitch * m_screenHeight);
        } else {
            // 逐行拷贝
            for (int y = 0; y < m_screenHeight; ++y) {
                memcpy(pDest, pSource, framePitch);
                pSource += rowPitch;
                pDest += framePitch;
            }
        }

        m_pContext->Unmap(m_pStagingTexture, 0);
        m_pDeskDupl->ReleaseFrame();

        width = m_screenWidth;
        height = m_screenHeight;

        return S_OK;
    }
};

int main() {
    std::cout << "=== Optimized Screen Capture Performance Test ===" << std::endl;

    OptimizedScreenCapture capture;
    
    if (!capture.Initialize()) {
        std::cerr << "Failed to initialize screen capture" << std::endl;
        return -1;
    }

    std::cout << "\n=== Test 1: Raw Performance (No Processing) ===" << std::endl;
    std::cout << "Testing pure DXGI capture speed for 10 seconds..." << std::endl;
    
    auto testStart = std::chrono::high_resolution_clock::now();
    auto testEnd = testStart + std::chrono::seconds(10);
    
    while (std::chrono::high_resolution_clock::now() < testEnd) {
        capture.CaptureRawPerformanceTest();
        // 不要任何延迟，测试最大性能
    }
    
    std::cout << "\n=== Test 2: With Minimal Processing ===" << std::endl;
    std::cout << "Testing with memory copy for 10 seconds..." << std::endl;
    
    testStart = std::chrono::high_resolution_clock::now();
    testEnd = testStart + std::chrono::seconds(10);
    
    while (std::chrono::high_resolution_clock::now() < testEnd) {
        capture.CaptureWithMinimalProcessing();
        // 不要任何延迟
    }

    std::cout << "\nPerformance test completed!" << std::endl;
    return 0;
}
