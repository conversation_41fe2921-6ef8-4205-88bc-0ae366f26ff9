﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0F4FB9A4-64D3-3E16-B516-2D37284279F4}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/pybind11/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/pybind11/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/pybind11/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/pybind11/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/pybind11/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/pybind11/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/pybind11/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/pybind11/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\foye_111111\build\ZERO_CHECK.vcxproj">
      <Project>{3A0889F4-9CE8-332E-933C-4A2A8424375E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>