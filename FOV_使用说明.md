# FOV瞄准补偿系统使用说明

## 概述

FOV（Field of View，视野角度）补偿系统可以根据游戏的FOV设置自动调整瞄准精度，确保在不同FOV下都能获得一致的瞄准体验。

## 为什么需要FOV补偿？

1. **角度与像素的关系**：不同FOV设置下，相同的游戏内角度对应不同的屏幕像素数量
2. **敏感度匹配**：游戏内鼠标敏感度通常基于角度，而不是像素
3. **精度问题**：不考虑FOV可能导致瞄准过度补偿或补偿不足

## 配置说明

### 1. 基础配置

在 `config.json` 中添加以下配置：

```json
{
    "game_settings": {
        "fov": 90,                          // 游戏FOV设置（度）
        "sensitivity": 1.0,                 // 游戏内鼠标敏感度
        "ads_sensitivity_multiplier": 1.0,  // 开镜敏感度倍数
        "enable_fov_compensation": true,    // 是否启用FOV补偿
        "game_type": "pubg"                 // 游戏类型
    }
}
```

### 2. 支持的游戏类型

- `pubg` - 绝地求生
- `csgo` - 反恐精英：全球攻势
- `valorant` - 无畏契约
- `apex` - Apex英雄
- `cod` - 使命召唤系列

### 3. 参数说明

- **fov**: 游戏中设置的FOV值（通常在60-120度之间）
- **sensitivity**: 游戏内鼠标敏感度设置
- **ads_sensitivity_multiplier**: 开镜时的敏感度倍数（如果游戏支持）
- **enable_fov_compensation**: 是否启用FOV补偿功能
- **game_type**: 游戏类型，用于应用特定的敏感度转换系数

## 使用方法

### 1. 手动配置

1. 打开游戏，查看当前的FOV设置
2. 记录游戏内的鼠标敏感度
3. 修改 `config.json` 中的相应参数
4. 重启瞄准程序

### 2. 使用校准工具

运行校准工具进行自动校准：

```bash
python fov_calibration_tool.py
```

校准步骤：
1. 按 'c' 开始校准
2. 按照屏幕指示，将准心对准不同位置的目标
3. 每个位置按空格键记录
4. 完成后工具会计算建议的敏感度设置
5. 按 'y' 应用设置，按 'n' 丢弃结果

### 3. 实时调整

程序运行时可以通过以下方式调整：

```python
# 在代码中动态更新FOV设置
fov_calculator.update_settings({
    "fov": 100,
    "sensitivity": 1.5
})
```

## 工作原理

### 1. 像素到角度转换

```
angle_per_pixel = FOV_degrees / screen_width_pixels
target_angle = pixel_error * angle_per_pixel
```

### 2. 角度到鼠标移动转换

```
effective_sensitivity = base_sensitivity * game_factor
if is_ads:
    effective_sensitivity *= ads_multiplier

mouse_movement = target_angle / effective_sensitivity
```

### 3. FOV补偿系数

```
compensation_factor = (90° / current_fov) / sensitivity
```

## 常见问题

### Q: 为什么启用FOV补偿后瞄准变得不准确？

A: 可能的原因：
1. FOV设置不正确 - 请确认游戏内的实际FOV值
2. 敏感度设置不匹配 - 使用校准工具重新校准
3. 游戏类型选择错误 - 确认选择了正确的游戏类型

### Q: 如何确定游戏的实际FOV？

A: 
1. 查看游戏设置中的FOV选项
2. 对于某些游戏，可能需要查看配置文件
3. 使用校准工具进行实际测量

### Q: 开镜时瞄准不准确怎么办？

A: 
1. 确认 `ads_sensitivity_multiplier` 设置正确
2. 某些游戏的开镜敏感度是独立设置的
3. 可能需要为开镜状态单独校准

### Q: 不同分辨率下需要重新配置吗？

A: 
通常不需要，FOV补偿会自动适应不同分辨率。但如果更改了游戏的渲染分辨率或显示比例，可能需要重新校准。

## 高级设置

### 1. 自定义游戏支持

如果你的游戏不在支持列表中，可以添加自定义配置：

```python
# 在 fov_calculator.py 中添加
self.game_sensitivity_factors = {
    "your_game": 1.0,  # 根据实际情况调整
    # ... 其他游戏
}
```

### 2. 精细调整

对于需要精细调整的情况，可以修改以下参数：

- `base_conversion_factor`: 基础转换系数
- `angle_per_pixel`: 每像素角度（自动计算）
- `sensitivity_factor`: 敏感度系数

### 3. 调试模式

启用调试模式查看详细信息：

```json
{
    "debug": {
        "show_timing": true,
        "show_fov_info": true
    }
}
```

## 性能影响

FOV补偿计算非常轻量，对程序性能的影响微乎其微：
- 额外计算时间：< 0.1ms
- 内存占用：< 1MB
- CPU使用率增加：< 1%

## 更新日志

- v1.0: 初始版本，支持基础FOV补偿
- v1.1: 添加校准工具
- v1.2: 支持开镜敏感度
- v1.3: 添加多游戏支持

## 技术支持

如果遇到问题，请提供以下信息：
1. 游戏名称和版本
2. 游戏内FOV和敏感度设置
3. 屏幕分辨率
4. 错误日志（如有）

---

**注意**: FOV补偿功能需要准确的游戏设置信息才能正常工作。建议在使用前先进行校准。
