

import time
from mouse_controller import move_mouse_ghub, mouse_left_down_ghub, mouse_left_up_ghub, GHUB_AVAILABLE

if not GHUB_AVAILABLE:
    print("MouseControl.dll 加载失败，无法测试！")
    exit(1)

print("3秒后开始测试鼠标移动和左键点击，请将鼠标放在安全区域！")
time.sleep(3)

print("测试：向右移动100像素")
move_mouse_ghub(100, 0)
time.sleep(1)

print("测试：按下左键")
mouse_left_down_ghub()
time.sleep(0.2)
print("测试：松开左键")
mouse_left_up_ghub()
print("测试完成。")