#include "../include/Common.h"
#include "../include/ConfigManager.h"
#include "../include/ScreenCapture.h"
#include "../include/ONNXInference.h"
#include "../include/PostProcessor.h"
#include "../include/MouseController.h"
#include "../include/PIDController.h"
#include "../include/AimingState.h"

/**
 * 视觉线程函数
 * 负责截图、推理和目标检测
 */
void VisionThreadFunc(
    std::shared_ptr<AimingState> state,
    std::shared_ptr<ConfigManager> config
) {
    LOG_INFO("[Vision Thread] Starting...");
    
    // 获取配置
    auto modelConfig = config->GetModelConfig();
    auto captureConfig = config->GetCaptureConfig();
    auto aimConfig = config->GetAimConfig();
    auto displayConfig = config->GetDisplayConfig();
    
    // 初始化截图器
    int roiSize = captureConfig.roiSize;
    int roiX = (captureConfig.screenWidth - roiSize) / 2;
    int roiY = (captureConfig.screenHeight - roiSize) / 2;
    
    auto capture = ScreenCaptureFactory::CreateScreenCapture(roiX, roiY, roiSize, roiSize, modelConfig.inputSize);
    if (!capture) {
        LOG_ERROR("[Vision Thread] Failed to create screen capture");
        state->RequestStop();
        return;
    }
    
    // 初始化推理引擎
    ONNXInference inference;
    if (inference.Initialize(modelConfig.modelPath) != ErrorCode::Success) {
        LOG_ERROR("[Vision Thread] Failed to initialize ONNX inference");
        state->RequestStop();
        return;
    }
    
    // 初始化后处理器
    PostProcessor postProcessor(modelConfig.confidenceThreshold, modelConfig.nmsThreshold, modelConfig.numClasses);
    
    // 创建显示窗口
    if (displayConfig.showPreview) {
        cv::namedWindow("AimAssist Preview", cv::WINDOW_NORMAL);
        cv::resizeWindow("AimAssist Preview", 640, 640);
    }
    
    LOG_INFO("[Vision Thread] Initialized successfully");
    
    // 主循环
    int frameCount = 0;
    auto lastFpsTime = Utils::GetCurrentTime();
    
    while (state->IsRunning()) {
        auto frameStart = Utils::GetCurrentTime();
        
        // 1. 截图
        cv::Mat inputImage = capture->CaptureAndPreprocess();
        if (inputImage.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }
        
        // 2. 推理
        auto inferenceOutput = inference.Inference(inputImage);
        if (inferenceOutput.empty()) {
            continue;
        }
        
        // 3. 后处理
        auto detections = postProcessor.ProcessOutput(
            inferenceOutput,
            cv::Size(modelConfig.inputSize, modelConfig.inputSize),
            cv::Size(roiSize, roiSize)
        );
        
        // 4. 检查瞄准按键状态
        bool isAiming = Utils::IsKeyPressed(aimConfig.aimKey1) || Utils::IsKeyPressed(aimConfig.aimKey2);
        state->UpdateAimStatus(isAiming);
        
        // 5. 选择最佳目标
        std::shared_ptr<Detection> bestTarget = nullptr;
        if (isAiming && !detections.empty()) {
            cv::Point2f crosshair(roiSize / 2.0f, roiSize / 2.0f);
            int aimPriority = Utils::IsKeyPressed(aimConfig.aimKey1) ? 1 : 2;
            
            bestTarget = postProcessor.SelectBestTarget(
                detections, aimPriority, crosshair, aimConfig.aimCircleRadius
            );
            
            if (bestTarget) {
                float offsetY = Utils::IsKeyPressed(aimConfig.aimKey1) ? aimConfig.offsetY1 : aimConfig.offsetY2;
                state->UpdateTarget(bestTarget, crosshair, offsetY);
            }
        }
        
        // 6. 显示预览
        if (displayConfig.showPreview) {
            cv::Mat displayImage;
            cv::resize(inputImage, displayImage, cv::Size(640, 640));
            cv::cvtColor(displayImage, displayImage, cv::COLOR_RGB2BGR);
            
            // 绘制检测结果
            cv::Point2f crosshair(320, 320);
            displayImage = postProcessor.DrawDetections(displayImage, detections, bestTarget, crosshair, aimConfig.aimCircleRadius);
            
            // 显示FPS和状态信息
            frameCount++;
            auto currentTime = Utils::GetCurrentTime();
            if (Utils::GetElapsedTime(lastFpsTime) >= 1000.0) {
                double fps = frameCount * 1000.0 / Utils::GetElapsedTime(lastFpsTime);
                std::string fpsText = "FPS: " + std::to_string((int)fps);
                cv::putText(displayImage, fpsText, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);
                
                frameCount = 0;
                lastFpsTime = currentTime;
            }
            
            // 显示瞄准状态
            std::string statusText = isAiming ? "AIMING" : "IDLE";
            cv::Scalar statusColor = isAiming ? cv::Scalar(0, 0, 255) : cv::Scalar(255, 255, 255);
            cv::putText(displayImage, statusText, cv::Point(10, 70), cv::FONT_HERSHEY_SIMPLEX, 1, statusColor, 2);
            
            cv::imshow("AimAssist Preview", displayImage);
            
            // 处理按键
            int key = cv::waitKey(1) & 0xFF;
            if (key == 27) { // ESC
                state->RequestStop();
                break;
            }
        }
        
        // 7. 更新性能统计
        PerformanceStats stats;
        stats.captureTimeMs = capture->GetPerformanceStats().captureTimeMs;
        stats.inferenceTimeMs = inference.GetPerformanceStats().inferenceTimeMs;
        stats.postprocessTimeMs = postProcessor.GetPerformanceStats().postprocessTimeMs;
        stats.totalTimeMs = Utils::GetElapsedTime(frameStart);
        stats.frameCount = frameCount;
        stats.UpdateFPS();
        
        state->UpdatePerformanceStats(stats);
        
        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    if (displayConfig.showPreview) {
        cv::destroyAllWindows();
    }
    
    LOG_INFO("[Vision Thread] Stopped");
}

/**
 * 控制线程函数
 * 负责鼠标移动控制
 */
void ControlThreadFunc(
    std::shared_ptr<AimingState> state,
    std::shared_ptr<ConfigManager> config
) {
    LOG_INFO("[Control Thread] Starting...");
    
    // 获取配置
    auto mouseConfig = config->GetMouseConfig();
    auto pidParams = config->GetPIDParams(mouseConfig.moveMode);
    
    // 初始化鼠标控制器
    auto mouseController = MouseControllerFactory::CreateMouseController(mouseConfig.moveMode, mouseConfig);
    if (!mouseController) {
        LOG_ERROR("[Control Thread] Failed to create mouse controller");
        state->RequestStop();
        return;
    }
    
    if (mouseController->Initialize() != ErrorCode::Success) {
        LOG_ERROR("[Control Thread] Failed to initialize mouse controller");
        state->RequestStop();
        return;
    }
    
    // 初始化PID控制器
    PIDController pidController(pidParams);
    
    LOG_INFO("[Control Thread] Initialized successfully with " << mouseController->GetTypeName() << " controller");
    
    // 控制循环
    double sampleTime = 1.0 / pidParams.controlFrequency;
    auto lastTime = Utils::GetCurrentTime();
    
    while (state->IsRunning()) {
        auto currentTime = Utils::GetCurrentTime();
        double deltaTime = Utils::GetElapsedTime(lastTime) / 1000.0; // 转换为秒
        
        if (deltaTime < sampleTime) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }
        
        // 获取控制信息
        bool isAiming;
        cv::Point2f totalError;
        std::shared_ptr<Detection> targetInfo;
        bool newTargetAvailable;
        
        if (state->GetControlInfo(isAiming, totalError, targetInfo, newTargetAvailable)) {
            if (isAiming && targetInfo) {
                // 计算PID输出
                cv::Point2f pidOutput = pidController.Compute(totalError, deltaTime);
                
                // 执行鼠标移动
                int moveX = static_cast<int>(pidOutput.x);
                int moveY = static_cast<int>(pidOutput.y);
                
                if (abs(moveX) > 0 || abs(moveY) > 0) {
                    mouseController->MoveMouse(moveX, moveY);
                }
            } else {
                // 重置PID控制器
                pidController.Reset();
            }
        }
        
        lastTime = currentTime;
        std::this_thread::sleep_for(std::chrono::microseconds(100)); // 高频控制
    }
    
    mouseController->Release();
    LOG_INFO("[Control Thread] Stopped");
}

/**
 * 主函数
 */
int main(int argc, char* argv[]) {
    LOG_INFO("=== AimAssist C++ Version Starting ===");
    
    // 解析命令行参数
    std::string configPath = "config.json";
    if (argc > 1) {
        configPath = argv[1];
    }
    
    // 初始化配置管理器
    auto config = std::make_shared<ConfigManager>();
    if (config->LoadConfig(configPath) != ErrorCode::Success) {
        LOG_ERROR("Failed to load configuration, using defaults");
    }
    
    config->PrintConfigSummary();
    
    // 初始化状态管理器
    auto state = std::make_shared<AimingState>();
    
    // 创建并启动线程
    std::thread visionThread(VisionThreadFunc, state, config);
    std::thread controlThread(ControlThreadFunc, state, config);
    
    LOG_INFO("All threads started successfully");
    LOG_INFO("Press ESC in preview window to exit");
    
    // 等待线程结束
    visionThread.join();
    controlThread.join();
    
    LOG_INFO("=== AimAssist C++ Version Stopped ===");
    return 0;
}
