#include "../include/ONNXInference.h"
#include <iostream>
#include <dml_provider_factory.h>

ONNXInference::ONNXInference(const std::string& modelPath, bool useGPU)
    : m_modelPath(modelPath)
    , m_inputWidth(320)
    , m_inputHeight(320)
    , m_inputChannels(3)
    , m_initialized(false)
    , m_useGPU(useGPU)
    , m_executionProvider("CPU")
    , m_lastInferenceTime(0.0)
    , m_avgInferenceTime(0.0)
    , m_inferenceCount(0)
{
}

ONNXInference::~ONNXInference() {
    // 智能指针会自动清理
}

bool ONNXInference::Initialize(const std::string& modelPath, bool useGPU) {
    if (m_initialized) {
        return true;
    }

    if (!modelPath.empty()) {
        m_modelPath = modelPath;
    }

    m_useGPU = useGPU;

    if (!LoadModel(m_modelPath)) {
        return false;
    }

    GetModelInfo();

    m_initialized = true;
    std::cout << "ONNX Inference initialized successfully" << std::endl;
    std::cout << "Model: " << m_modelPath << std::endl;
    std::cout << "Execution Provider: " << m_executionProvider << std::endl;
    std::cout << "Input size: " << m_inputWidth << "x" << m_inputHeight << "x" << m_inputChannels << std::endl;

    return true;
}

std::vector<float> ONNXInference::Inference(const cv::Mat& inputImage) {
    if (!m_initialized || !m_session) {
        return {};
    }

    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // 预处理图像
        auto inputTensor = PreprocessImage(inputImage);

        // 创建输入张量
        std::vector<int64_t> inputShape = {1, m_inputChannels, m_inputHeight, m_inputWidth};
        auto memoryInfo = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
        Ort::Value inputOrtTensor = Ort::Value::CreateTensor<float>(
            memoryInfo, inputTensor.data(), inputTensor.size(), inputShape.data(), inputShape.size());

        // 执行推理
        auto outputTensors = m_session->Run(Ort::RunOptions{nullptr},
                                          m_inputNames.data(), &inputOrtTensor, 1,
                                          m_outputNames.data(), m_outputNames.size());

        // 获取输出数据
        float* outputData = outputTensors[0].GetTensorMutableData<float>();
        auto outputShape = outputTensors[0].GetTensorTypeAndShapeInfo().GetShape();

        size_t outputSize = 1;
        for (auto dim : outputShape) {
            outputSize *= dim;
        }

        std::vector<float> result(outputData, outputData + outputSize);

        auto endTime = std::chrono::high_resolution_clock::now();
        double inferenceTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
        UpdatePerformanceStats(inferenceTime);

        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "ONNX inference failed: " << e.what() << std::endl;
        return {};
    }
}

bool ONNXInference::LoadModel(const std::string& modelPath) {
    try {
        // 创建环境
        m_env = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "AimAssist");

        // 配置会话选项
        m_sessionOptions = std::make_unique<Ort::SessionOptions>();
        m_sessionOptions->SetIntraOpNumThreads(1);
        m_sessionOptions->SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);

        // 暂时只使用CPU提供程序，避免DirectML问题
        m_executionProvider = "CPU";
        std::cout << "Using CPU execution provider" << std::endl;

        // 创建会话
        std::wstring wModelPath(modelPath.begin(), modelPath.end());
        m_session = std::make_unique<Ort::Session>(*m_env, wModelPath.c_str(), *m_sessionOptions);

        std::cout << "ONNX model loaded successfully: " << modelPath << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to load ONNX model: " << e.what() << std::endl;
        return false;
    }
}

void ONNXInference::GetModelInfo() {
    if (!m_session) return;

    // 获取输入信息
    size_t numInputNodes = m_session->GetInputCount();
    m_inputNameStrings.clear();
    m_inputNames.clear();

    for (size_t i = 0; i < numInputNodes; i++) {
        auto inputName = m_session->GetInputNameAllocated(i, m_allocator);
        m_inputNameStrings.push_back(std::string(inputName.get()));
        m_inputNames.push_back(m_inputNameStrings.back().c_str());

        auto inputTypeInfo = m_session->GetInputTypeInfo(i);
        auto inputTensorInfo = inputTypeInfo.GetTensorTypeAndShapeInfo();
        m_inputShape = inputTensorInfo.GetShape();
    }

    // 获取输出信息
    size_t numOutputNodes = m_session->GetOutputCount();
    m_outputNameStrings.clear();
    m_outputNames.clear();

    for (size_t i = 0; i < numOutputNodes; i++) {
        auto outputName = m_session->GetOutputNameAllocated(i, m_allocator);
        m_outputNameStrings.push_back(std::string(outputName.get()));
        m_outputNames.push_back(m_outputNameStrings.back().c_str());

        auto outputTypeInfo = m_session->GetOutputTypeInfo(i);
        auto outputTensorInfo = outputTypeInfo.GetTensorTypeAndShapeInfo();
        m_outputShape = outputTensorInfo.GetShape();
    }

    // 更新输入尺寸
    if (m_inputShape.size() >= 4) {
        m_inputChannels = static_cast<int>(m_inputShape[1]);
        m_inputHeight = static_cast<int>(m_inputShape[2]);
        m_inputWidth = static_cast<int>(m_inputShape[3]);
    }
}

std::vector<float> ONNXInference::PreprocessImage(const cv::Mat& image) {
    cv::Mat processedImage;
    
    // 确保图像是正确的尺寸
    if (image.size() != cv::Size(m_inputWidth, m_inputHeight)) {
        cv::resize(image, processedImage, cv::Size(m_inputWidth, m_inputHeight));
    } else {
        processedImage = image;
    }

    // 转换为float并归一化到[0,1]
    processedImage.convertTo(processedImage, CV_32F, 1.0 / 255.0);

    // 转换为CHW格式 (从HWC到CHW)
    std::vector<cv::Mat> channels(3);
    cv::split(processedImage, channels);

    std::vector<float> result;
    result.reserve(m_inputChannels * m_inputHeight * m_inputWidth);

    for (int c = 0; c < m_inputChannels; ++c) {
        float* data = reinterpret_cast<float*>(channels[c].data);
        result.insert(result.end(), data, data + m_inputHeight * m_inputWidth);
    }

    return result;
}

void ONNXInference::UpdatePerformanceStats(double inferenceTime) const {
    m_inferenceCount++;
    m_lastInferenceTime = inferenceTime;

    // 简单的移动平均
    if (m_inferenceCount == 1) {
        m_avgInferenceTime = inferenceTime;
    } else {
        m_avgInferenceTime = (m_avgInferenceTime * 0.9) + (inferenceTime * 0.1);
    }
}
