﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{32DFC9C7-FE68-3052-998A-43CEEC3B61B5}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>aim_controller</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">aim_controller.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">aim_controller.cp39-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">aim_controller.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">aim_controller.cp39-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">aim_controller.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">aim_controller.cp39-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">aim_controller.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">aim_controller.cp39-win_amd64</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.pyd</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug";aim_controller_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\";aim_controller_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480d.lib;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\libs\python39.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build/Debug/aim_controller.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build/Debug/aim_controller.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release";aim_controller_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\";aim_controller_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\libs\python39.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build/Release/aim_controller.lib</ImportLibrary>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build/Release/aim_controller.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel";aim_controller_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\";aim_controller_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\libs\python39.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build/MinSizeRel/aim_controller.lib</ImportLibrary>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build/MinSizeRel/aim_controller.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo";aim_controller_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\";aim_controller_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\Programs\Python\Python39\include;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\include;D:\3rd_party\opencv_4.8\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\opencv_world480.lib;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\libs\python39.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/foye_111111/build/RelWithDebInfo/aim_controller.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/foye_111111/build/RelWithDebInfo/aim_controller.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-file C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\src\AimController.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\src\KalmanTracker.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\src\bindings.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\foye_111111\build\ZERO_CHECK.vcxproj">
      <Project>{3A0889F4-9CE8-332E-933C-4A2A8424375E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>