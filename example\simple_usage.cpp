/**
 * 简单使用示例
 * 演示如何使用C++截图模块进行基本的屏幕捕获和预处理
 */

#include "../include/ScreenCapture.h"
#include <iostream>
#include <chrono>
#include <thread>

int main() {
    std::cout << "=== 简单截图示例 ===" << std::endl;

    // 1. 检查系统支持
    if (!ScreenCaptureFactory::IsDXGISupported()) {
        std::cerr << "系统不支持DXGI截图" << std::endl;
        return -1;
    }

    // 2. 创建截图器 (屏幕中心640x640区域，输出320x320)
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    
    int roiSize = 640;
    int roiX = (screenWidth - roiSize) / 2;
    int roiY = (screenHeight - roiSize) / 2;
    
    auto capture = ScreenCaptureFactory::CreateScreenCapture(roiX, roiY, roiSize, roiSize, 320);
    if (!capture) {
        std::cerr << "创建截图器失败" << std::endl;
        return -1;
    }

    std::cout << "截图器初始化成功" << std::endl;
    std::cout << "ROI: (" << roiX << ", " << roiY << ", " << roiSize << ", " << roiSize << ")" << std::endl;

    // 3. 截图循环
    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();

    while (frameCount < 1000) { // 截取1000帧
        cv::Mat frame = capture->CaptureAndPreprocess();
        
        if (frame.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        frameCount++;

        // 每100帧显示一次进度
        if (frameCount % 100 == 0) {
            auto stats = capture->GetPerformanceStats();
            std::cout << "帧数: " << frameCount 
                      << ", 截图: " << stats.captureTimeMs << "ms"
                      << ", 预处理: " << stats.preprocessTimeMs << "ms" << std::endl;
        }

        // 验证输出格式
        if (frameCount == 1) {
            std::cout << "输出图像格式验证:" << std::endl;
            std::cout << "  尺寸: " << frame.cols << "x" << frame.rows << std::endl;
            std::cout << "  通道数: " << frame.channels() << std::endl;
            std::cout << "  数据类型: " << frame.type() << " (应该是16，即CV_8UC3)" << std::endl;
            
            // 保存第一帧作为示例
            cv::Mat bgrFrame;
            cv::cvtColor(frame, bgrFrame, cv::COLOR_RGB2BGR);
            cv::imwrite("first_frame.jpg", bgrFrame);
            std::cout << "  已保存第一帧: first_frame.jpg" << std::endl;
        }
    }

    // 4. 性能统计
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    double avgFps = frameCount * 1000.0 / duration.count();

    std::cout << "\n=== 性能统计 ===" << std::endl;
    std::cout << "总帧数: " << frameCount << std::endl;
    std::cout << "总时间: " << duration.count() << " ms" << std::endl;
    std::cout << "平均FPS: " << avgFps << std::endl;

    auto finalStats = capture->GetPerformanceStats();
    std::cout << "平均截图时间: " << finalStats.captureTimeMs << " ms" << std::endl;
    std::cout << "平均预处理时间: " << finalStats.preprocessTimeMs << " ms" << std::endl;
    std::cout << "平均总时间: " << finalStats.totalTimeMs << " ms" << std::endl;

    std::cout << "\n测试完成!" << std::endl;
    return 0;
}

/**
 * 高级使用示例
 * 演示如何集成到AI推理流程中
 */
class AIInferenceExample {
private:
    std::unique_ptr<ScreenCapture> m_capture;
    
public:
    bool Initialize(int roiX, int roiY, int roiSize) {
        m_capture = ScreenCaptureFactory::CreateScreenCapture(roiX, roiY, roiSize, roiSize, 320);
        return m_capture != nullptr;
    }
    
    /**
     * 模拟AI推理流程
     */
    void RunInferenceLoop() {
        std::cout << "\n=== AI推理流程示例 ===" << std::endl;
        
        int inferenceCount = 0;
        auto startTime = std::chrono::high_resolution_clock::now();
        
        while (inferenceCount < 100) {
            // 1. 截图和预处理
            cv::Mat inputImage = m_capture->CaptureAndPreprocess();
            if (inputImage.empty()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }
            
            // 2. 转换为模型输入格式 (1, 3, 320, 320)
            cv::Mat floatImage;
            inputImage.convertTo(floatImage, CV_32F, 1.0/255.0); // 归一化到[0,1]
            
            // 3. 模拟推理过程 (这里只是简单的延时)
            std::this_thread::sleep_for(std::chrono::milliseconds(10)); // 模拟10ms推理时间
            
            // 4. 模拟后处理
            // 这里可以添加NMS、置信度过滤等逻辑
            
            inferenceCount++;
            
            if (inferenceCount % 20 == 0) {
                std::cout << "推理进度: " << inferenceCount << "/100" << std::endl;
            }
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        double avgFps = inferenceCount * 1000.0 / duration.count();
        
        std::cout << "AI推理性能: " << avgFps << " FPS" << std::endl;
    }
};

// 如果需要运行高级示例，取消下面的注释
/*
int main() {
    AIInferenceExample example;
    
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    int roiSize = 640;
    int roiX = (screenWidth - roiSize) / 2;
    int roiY = (screenHeight - roiSize) / 2;
    
    if (example.Initialize(roiX, roiY, roiSize)) {
        example.RunInferenceLoop();
    }
    
    return 0;
}
*/
