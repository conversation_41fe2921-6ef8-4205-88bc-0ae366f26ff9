# 🎯 自瞄程序配置系统

## 📋 概述

本程序现在支持外部配置文件，您可以通过修改 `config.json` 文件来调整所有参数，无需修改代码。这使得程序更容易分发和自定义。

## 🗂️ 文件结构

```
📁 程序目录/
├── 📄 main_enhanced.py          # 主程序
├── 📄 config.json               # 配置文件 (可编辑)
├── 📄 config_loader.py          # 配置加载器
├── 📄 config_editor.py          # 图形配置编辑器
├── 📄 test_config.py            # 配置测试工具
├── 📄 启动程序.bat              # 启动脚本
├── 📄 配置说明.md               # 详细配置说明
└── 📄 README_配置系统.md        # 本文件
```

## 🚀 快速开始

### 方法1: 使用启动脚本 (推荐)
1. 双击 `启动程序.bat`
2. 选择相应的操作

### 方法2: 手动操作
1. **运行程序**: `python main_enhanced.py`
2. **编辑配置**: `python config_editor.py`
3. **测试配置**: `python test_config.py`

## ⚙️ 配置文件说明

### 🎯 主要配置项

| 配置项 | 说明 | 推荐值 |
|--------|------|--------|
| `confidence_threshold` | 检测置信度 | 0.65 |
| `nms_threshold` | NMS阈值 | 0.2 |
| `move_mode` | 移动方式 | "ghub"/"kmbox"/"system" |
| `aim_circle_radius` | 瞄准圆环半径 | 90 |
| `control_frequency` | 控制频率 | 500 |

### 🎮 扳机配置

```json
"trigger": {
    "enabled": true,           // 是否启用扳机
    "fire_delay_ms": 50,       // 开火延迟(毫秒)
    "fire_interval_ms": 120    // 开火间隔(毫秒)
}
```

### 🔧 PID参数

```json
"pid": {
    "system/kmbox/ghub": {
        "kp": 50.0,            // 比例增益
        "ki": 0.0,             // 积分增益
        "kd": 0.005,           // 微分增益
        "inner_threshold": 20.0,
        "outer_threshold": 50.0
    }
}
```

## 🛠️ 配置工具

### 1. 图形配置编辑器
- **启动**: `python config_editor.py`
- **功能**: 可视化编辑所有配置项
- **优点**: 直观易用，实时预览

### 2. 配置测试工具
- **启动**: `python test_config.py`
- **功能**: 验证配置文件格式和内容
- **输出**: 详细的配置摘要和建议

### 3. 手动编辑
- **文件**: `config.json`
- **工具**: 任何文本编辑器
- **注意**: 确保JSON格式正确

## 📝 使用流程

### 新用户
1. 运行 `test_config.py` 了解当前配置
2. 使用 `config_editor.py` 调整基本参数
3. 先禁用扳机功能进行测试
4. 逐步调整参数直到满意

### 高级用户
1. 直接编辑 `config.json` 文件
2. 启用扳机并调整延迟参数
3. 根据硬件优化PID参数
4. 使用 `test_config.py` 验证配置

## 🔍 故障排除

### 配置文件问题
- **症状**: 程序启动失败
- **原因**: JSON格式错误
- **解决**: 运行 `test_config.py` 检查格式

### 扳机不工作
- **检查**: `trigger.enabled` 是否为 `true`
- **检查**: 是否有有效目标
- **检查**: 按键代码是否正确

### 瞄准不准确
- **调整**: 降低 `confidence_threshold`
- **调整**: 增大 `aim_circle_radius`
- **调整**: 优化PID参数

## 📊 配置建议

### 🎯 精度优先
```json
{
    "confidence_threshold": 0.7,
    "nms_threshold": 0.3,
    "aim_circle_radius": 120,
    "trigger": {"enabled": false}
}
```

### ⚡ 速度优先
```json
{
    "confidence_threshold": 0.6,
    "nms_threshold": 0.2,
    "control_frequency": 600,
    "trigger": {"enabled": true, "fire_interval_ms": 80}
}
```

### 🎮 游戏优化
```json
{
    "confidence_threshold": 0.65,
    "aim_circle_radius": 90,
    "trigger": {
        "enabled": true,
        "fire_delay_ms": 30,
        "fire_interval_ms": 100
    }
}
```

## 🔄 版本升级

当程序更新时：
1. 备份当前 `config.json`
2. 运行新版本程序（会自动生成新配置）
3. 手动合并您的自定义设置
4. 运行 `test_config.py` 验证

## 💡 高级技巧

### 多配置管理
```bash
# 备份当前配置
copy config.json config_backup.json

# 创建游戏专用配置
copy config.json config_game1.json

# 切换配置
copy config_game1.json config.json
```

### 性能调优
- 监控 `debug.show_timing` 输出
- 根据硬件调整 `control_frequency`
- 平衡精度和性能

### 安全设置
- 新环境先禁用扳机
- 逐步增加检测阈值
- 定期备份有效配置

## 📞 支持

如果遇到问题：
1. 运行 `test_config.py` 诊断
2. 查看 `配置说明.md` 详细文档
3. 恢复默认配置重新开始

---

**🎉 享受您的自定义配置体验！**
