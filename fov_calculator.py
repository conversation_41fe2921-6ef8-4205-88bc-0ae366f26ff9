import math
import numpy as np
from typing import Tuple, Dict, Any

class FOVCalculator:
    """
    FOV计算器，用于将像素误差转换为考虑FOV的角度误差，
    并计算相应的鼠标移动量。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化FOV计算器
        
        参数:
            config: 包含游戏设置的配置字典
        """
        self.game_settings = config.get("game_settings", {})
        self.capture_settings = config.get("capture", {})
        
        # 游戏设置
        self.fov = self.game_settings.get("fov", 90)  # 游戏FOV（度）
        self.sensitivity = self.game_settings.get("sensitivity", 1.0)  # 游戏敏感度
        self.ads_sensitivity_multiplier = self.game_settings.get("ads_sensitivity_multiplier", 1.0)  # 开镜敏感度倍数
        self.enable_fov_compensation = self.game_settings.get("enable_fov_compensation", True)
        self.game_type = self.game_settings.get("game_type", "pubg")
        
        # 屏幕设置
        self.screen_width, self.screen_height = self.capture_settings.get("screen_resolution", [1920, 1080])
        self.roi_size = self.capture_settings.get("roi_size", 640)
        
        # 计算基础参数
        self._calculate_base_parameters()
        
        print(f"[FOV计算器] 初始化完成:")
        print(f"  - 游戏FOV: {self.fov}°")
        print(f"  - 游戏敏感度: {self.sensitivity}")
        print(f"  - 开镜敏感度倍数: {self.ads_sensitivity_multiplier}")
        print(f"  - FOV补偿: {'启用' if self.enable_fov_compensation else '禁用'}")
        print(f"  - 每像素角度: {self.angle_per_pixel:.6f}°/px")
    
    def _calculate_base_parameters(self):
        """计算基础参数"""
        # 计算水平FOV对应的每像素角度
        # 使用ROI区域的宽度作为计算基准
        self.angle_per_pixel = self.fov / self.roi_size
        
        # 不同游戏的敏感度转换系数
        self.game_sensitivity_factors = {
            "pubg": 1.0,
            "csgo": 0.022,  # CSGO特殊的敏感度转换系数
            "valorant": 1.0,
            "apex": 1.0,
            "cod": 1.0
        }
        
        self.sensitivity_factor = self.game_sensitivity_factors.get(self.game_type.lower(), 1.0)
    
    def pixel_to_angle(self, pixel_error: np.ndarray) -> np.ndarray:
        """
        将像素误差转换为角度误差
        
        参数:
            pixel_error: [dx, dy] 像素误差
            
        返回:
            [angle_x, angle_y] 角度误差（度）
        """
        if not self.enable_fov_compensation:
            # 如果禁用FOV补偿，直接返回像素值
            return pixel_error
        
        # 水平角度误差
        angle_x = pixel_error[0] * self.angle_per_pixel
        
        # 垂直角度误差（考虑屏幕比例）
        # 垂直FOV通常根据水平FOV和屏幕比例计算
        vertical_fov = 2 * math.atan(math.tan(math.radians(self.fov / 2)) * (self.roi_size / self.roi_size)) * 180 / math.pi
        angle_per_pixel_y = vertical_fov / self.roi_size
        angle_y = pixel_error[1] * angle_per_pixel_y
        
        return np.array([angle_x, angle_y])
    
    def angle_to_mouse_movement(self, angle_error: np.ndarray, is_ads: bool = False) -> np.ndarray:
        """
        将角度误差转换为鼠标移动量
        
        参数:
            angle_error: [angle_x, angle_y] 角度误差（度）
            is_ads: 是否在开镜状态
            
        返回:
            [mouse_dx, mouse_dy] 鼠标移动量（像素）
        """
        if not self.enable_fov_compensation:
            # 如果禁用FOV补偿，直接返回角度值作为像素值
            return angle_error
        
        # 计算有效敏感度
        effective_sensitivity = self.sensitivity * self.sensitivity_factor
        if is_ads:
            effective_sensitivity *= self.ads_sensitivity_multiplier
        
        # 角度转鼠标移动的基础公式
        # 这个系数可能需要根据具体游戏进行调整
        base_conversion_factor = 1.0  # 基础转换系数，可能需要校准
        
        mouse_movement = angle_error / (effective_sensitivity * base_conversion_factor)
        
        return mouse_movement
    
    def pixel_to_mouse_movement(self, pixel_error: np.ndarray, is_ads: bool = False) -> np.ndarray:
        """
        直接将像素误差转换为鼠标移动量（考虑FOV）
        
        参数:
            pixel_error: [dx, dy] 像素误差
            is_ads: 是否在开镜状态
            
        返回:
            [mouse_dx, mouse_dy] 鼠标移动量（像素）
        """
        if not self.enable_fov_compensation:
            # 如果禁用FOV补偿，直接返回像素误差
            return pixel_error
        
        # 先转换为角度，再转换为鼠标移动
        angle_error = self.pixel_to_angle(pixel_error)
        mouse_movement = self.angle_to_mouse_movement(angle_error, is_ads)
        
        return mouse_movement
    
    def get_fov_compensation_factor(self, is_ads: bool = False) -> float:
        """
        获取FOV补偿系数
        
        参数:
            is_ads: 是否在开镜状态
            
        返回:
            补偿系数
        """
        if not self.enable_fov_compensation:
            return 1.0
        
        # 基础FOV补偿系数
        base_factor = 90.0 / self.fov  # 以90度FOV为基准
        
        # 考虑敏感度的影响
        sensitivity_factor = 1.0 / self.sensitivity
        if is_ads:
            sensitivity_factor /= self.ads_sensitivity_multiplier
        
        return base_factor * sensitivity_factor
    
    def update_settings(self, new_settings: Dict[str, Any]):
        """
        更新游戏设置
        
        参数:
            new_settings: 新的设置字典
        """
        self.game_settings.update(new_settings)
        
        # 更新相关参数
        self.fov = self.game_settings.get("fov", self.fov)
        self.sensitivity = self.game_settings.get("sensitivity", self.sensitivity)
        self.ads_sensitivity_multiplier = self.game_settings.get("ads_sensitivity_multiplier", self.ads_sensitivity_multiplier)
        self.enable_fov_compensation = self.game_settings.get("enable_fov_compensation", self.enable_fov_compensation)
        
        # 重新计算基础参数
        self._calculate_base_parameters()
        
        print(f"[FOV计算器] 设置已更新:")
        print(f"  - 游戏FOV: {self.fov}°")
        print(f"  - 游戏敏感度: {self.sensitivity}")
        print(f"  - 开镜敏感度倍数: {self.ads_sensitivity_multiplier}")
        print(f"  - FOV补偿: {'启用' if self.enable_fov_compensation else '禁用'}")
    
    def calibrate_sensitivity(self, pixel_distance: float, actual_mouse_movement: float, is_ads: bool = False) -> float:
        """
        校准敏感度设置
        
        参数:
            pixel_distance: 屏幕上的像素距离
            actual_mouse_movement: 实际需要的鼠标移动量
            is_ads: 是否在开镜状态
            
        返回:
            建议的敏感度值
        """
        angle_distance = pixel_distance * self.angle_per_pixel
        
        # 计算理想的敏感度
        ideal_sensitivity = angle_distance / actual_mouse_movement
        
        if is_ads:
            ideal_sensitivity /= self.ads_sensitivity_multiplier
        
        print(f"[FOV计算器] 敏感度校准建议:")
        print(f"  - 像素距离: {pixel_distance:.1f}px")
        print(f"  - 角度距离: {angle_distance:.3f}°")
        print(f"  - 实际鼠标移动: {actual_mouse_movement:.1f}px")
        print(f"  - 建议敏感度: {ideal_sensitivity:.3f}")
        
        return ideal_sensitivity

def create_fov_calculator(config: Dict[str, Any]) -> FOVCalculator:
    """
    创建FOV计算器实例
    
    参数:
        config: 配置字典
        
    返回:
        FOVCalculator实例
    """
    return FOVCalculator(config)
