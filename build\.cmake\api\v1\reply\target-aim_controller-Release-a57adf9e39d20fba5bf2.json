{"artifacts": [{"path": "Release/aim_controller.cp39-win_amd64.pyd"}, {"path": "Release/aim_controller.lib"}, {"path": "Release/aim_controller.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_library", "__Python_add_library", "python_add_library", "pybind11_add_module", "target_link_libraries"], "files": ["D:/C++/share/cmake-3.23/Modules/FindPython/Support.cmake", "D:/C++/share/cmake-3.23/Modules/FindPython.cmake", "pybind11/tools/pybind11NewTools.cmake", "CMakeLists.txt"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 25, "parent": 0}, {"command": 2, "file": 2, "line": 283, "parent": 1}, {"command": 1, "file": 1, "line": 580, "parent": 2}, {"command": 0, "file": 0, "line": 3331, "parent": 3}, {"command": 4, "file": 2, "line": 324, "parent": 1}, {"command": 4, "file": 3, "line": 26, "parent": 0}, {"command": 4, "file": 0, "line": 3354, "parent": 3}, {"command": 4, "file": 2, "line": 338, "parent": 1}, {"command": 4, "file": 2, "line": 290, "parent": 1}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /W3 /GR /EHsc /MD /O2 /Ob2 /DNDEBUG"}, {"backtrace": 5, "fragment": "/GL"}, {"backtrace": 8, "fragment": "/bigobj"}, {"backtrace": 8, "fragment": "/MP"}, {"fragment": "-std:c++17"}], "defines": [{"define": "aim_controller_EXPORTS"}], "includes": [{"backtrace": 7, "isSystem": true, "path": "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/include"}, {"backtrace": 9, "isSystem": true, "path": "C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller/pybind11/include"}, {"backtrace": 6, "isSystem": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/include"}], "language": "CXX", "languageStandard": {"backtraces": [9, 9, 9, 6], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "aim_controller::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/machine:x64 /INCREMENTAL:NO", "role": "flags"}, {"backtrace": 5, "fragment": "-LTCG", "role": "flags"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "D:\\3rd_party\\opencv_4.8\\opencv\\build\\x64\\vc16\\lib\\opencv_world480.lib", "role": "libraries"}, {"backtrace": 7, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\libs\\python39.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "aim_controller", "nameOnDisk": "aim_controller.cp39-win_amd64.pyd", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2]}], "sources": [{"backtrace": 4, "compileGroupIndex": 0, "path": "src/AimController.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/KalmanTracker.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/bindings.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}