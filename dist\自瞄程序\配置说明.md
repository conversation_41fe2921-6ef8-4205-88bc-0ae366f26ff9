# 配置文件说明 (config.json)

本程序使用外部配置文件 `config.json` 来管理所有参数，您可以直接修改这个文件来调整程序行为，无需修改代码。

## 📁 配置文件结构

### 🎯 模型配置 (model)
```json
"model": {
    "path": "models/PUBGV8_320.onnx",     // 模型文件路径
    "confidence_threshold": 0.65,          // 检测置信度阈值 (0.0-1.0)
    "nms_threshold": 0.2,                  // 非极大值抑制阈值 (0.0-1.0)
    "model_type": "yolov8",                // 模型类型: "yolov5" 或 "yolov8"
    "num_classes": 0,                      // 类别数量，0为自动检测
    "class_names": []                      // 类别名称列表
}
```

### 📸 截图配置 (capture)
```json
"capture": {
    "method": "screen",                    // 截图方式: "screen" 或 "udp"
    "screen_resolution": [1920, 1080],     // 屏幕分辨率
    "roi_size": 640,                       // 感兴趣区域大小
    "udp": {                               // UDP流配置（当method为"udp"时使用）
        "host": "127.0.0.1",
        "port": 8000,
        "buffer_size": 65536
    }
}
```

### 🖱️ 鼠标配置 (mouse)
```json
"mouse": {
    "move_mode": "ghub",                   // 移动方式: "system", "kmbox", "ghub"
    "kmbox": {                             // KMBox硬件配置
        "ip": "*************",
        "port": "8808",
        "mac": "62587019",
        "move_type": "normal"
    },
    "aim_keys": {                          // 自瞄按键配置
        "priority_1": [...],               // 优先级1按键
        "priority_2": [...]                // 优先级2按键
    }
}
```

### 🎮 按键配置详解 (aim_keys)
每个按键配置包含以下参数：
```json
{
    "key": 5,                              // 按键代码 (1=左键, 2=右键, 5=下侧键, 6=上侧键)
    "offset_y": 0.3,                       // Y轴偏移 (0.0-1.0, 0.0=顶部, 1.0=底部)
    "trigger": {                           // 扳机配置
        "enabled": true,                   // 是否启用扳机
        "fire_delay_ms": 50,               // 开火延迟(毫秒)
        "fire_interval_ms": 120            // 开火间隔(毫秒)
    }
}
```

### ⚙️ PID控制器配置 (pid)
```json
"pid": {
    "system": {                            // 系统移动PID参数
        "kp": 50.0,                        // 比例增益
        "ki": 0.0,                         // 积分增益
        "kd": 0.005,                       // 微分增益
        "base_ki": 0.0,                    // 基础积分增益
        "max_ki": 80.0,                    // 最大积分增益
        "inner_threshold": 20.0,           // 内部阈值
        "outer_threshold": 50.0            // 外部阈值
    },
    "kmbox": { ... },                      // KMBox移动PID参数
    "ghub": { ... },                       // GHub移动PID参数
    "control_frequency": 500               // 控制频率(Hz)
}
```

## 🔧 常用调整参数

### 提高检测精度
- 降低 `confidence_threshold` (如 0.5)
- 降低 `nms_threshold` (如 0.1)

### 调整瞄准速度
- 增大 `kp` 值：更快的瞄准速度
- 增大 `kd` 值：更好的稳定性
- 调整 `control_frequency`：更高的控制频率

### 扳机设置
- `fire_delay_ms`：瞄准到目标后延迟多久开火
- `fire_interval_ms`：两次开火之间的最小间隔
- `enabled`：是否启用自动开火

### 瞄准区域
- `aim_circle_radius`：自瞄圆环半径（像素）
- `offset_y`：瞄准点在目标框中的位置

## 🚀 使用方法

1. **修改配置**：直接编辑 `config.json` 文件
2. **重启程序**：配置会在程序启动时自动加载
3. **查看效果**：程序启动时会显示当前配置摘要

## ⚠️ 注意事项

1. **JSON格式**：确保配置文件是有效的JSON格式
2. **备份配置**：修改前建议备份原配置文件
3. **参数范围**：注意参数的有效范围，避免设置无效值
4. **重启生效**：配置修改后需要重启程序才能生效

## 🎯 推荐配置

### 新手配置
```json
{
    "model": {
        "confidence_threshold": 0.7,
        "nms_threshold": 0.3
    },
    "mouse": {
        "aim_keys": {
            "priority_1": [{
                "key": 5,
                "trigger": {"enabled": false}
            }]
        }
    }
}
```

### 高级配置
```json
{
    "model": {
        "confidence_threshold": 0.6,
        "nms_threshold": 0.2
    },
    "mouse": {
        "aim_keys": {
            "priority_2": [{
                "key": 6,
                "trigger": {
                    "enabled": true,
                    "fire_delay_ms": 30,
                    "fire_interval_ms": 80
                }
            }]
        }
    }
}
```

## 🆘 故障排除

- **配置不生效**：检查JSON格式是否正确
- **程序启动失败**：查看控制台错误信息
- **扳机不工作**：确认 `trigger.enabled` 为 `true`
- **瞄准不准确**：调整PID参数或检测阈值
