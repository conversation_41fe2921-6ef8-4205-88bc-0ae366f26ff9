﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{E71B64EC-2443-3B80-BC53-1FC17AB59658}"
	ProjectSection(ProjectDependencies) = postProject
		{424311D7-1F7F-36EE-BC75-2128F522963B} = {424311D7-1F7F-36EE-BC75-2128F522963B}
		{657D5982-5DEA-3249-A6BB-B87DC13748E6} = {657D5982-5DEA-3249-A6BB-B87DC13748E6}
		{8E5582DE-331C-3E77-99EA-F50C68558F30} = {8E5582DE-331C-3E77-99EA-F50C68558F30}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "AimAssist_CPP_lib", "AimAssist_CPP_lib.vcxproj", "{424311D7-1F7F-36EE-BC75-2128F522963B}"
	ProjectSection(ProjectDependencies) = postProject
		{657D5982-5DEA-3249-A6BB-B87DC13748E6} = {657D5982-5DEA-3249-A6BB-B87DC13748E6}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{91E923F6-518C-3B25-9041-3D5F8A5A3589}"
	ProjectSection(ProjectDependencies) = postProject
		{E71B64EC-2443-3B80-BC53-1FC17AB59658} = {E71B64EC-2443-3B80-BC53-1FC17AB59658}
		{657D5982-5DEA-3249-A6BB-B87DC13748E6} = {657D5982-5DEA-3249-A6BB-B87DC13748E6}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{657D5982-5DEA-3249-A6BB-B87DC13748E6}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_capture", "test_capture.vcxproj", "{8E5582DE-331C-3E77-99EA-F50C68558F30}"
	ProjectSection(ProjectDependencies) = postProject
		{424311D7-1F7F-36EE-BC75-2128F522963B} = {424311D7-1F7F-36EE-BC75-2128F522963B}
		{657D5982-5DEA-3249-A6BB-B87DC13748E6} = {657D5982-5DEA-3249-A6BB-B87DC13748E6}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.Debug|x64.ActiveCfg = Debug|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.Debug|x64.Build.0 = Debug|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.Release|x64.ActiveCfg = Release|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.Release|x64.Build.0 = Release|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E71B64EC-2443-3B80-BC53-1FC17AB59658}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.Debug|x64.ActiveCfg = Debug|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.Debug|x64.Build.0 = Debug|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.Release|x64.ActiveCfg = Release|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.Release|x64.Build.0 = Release|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{424311D7-1F7F-36EE-BC75-2128F522963B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{91E923F6-518C-3B25-9041-3D5F8A5A3589}.Debug|x64.ActiveCfg = Debug|x64
		{91E923F6-518C-3B25-9041-3D5F8A5A3589}.Release|x64.ActiveCfg = Release|x64
		{91E923F6-518C-3B25-9041-3D5F8A5A3589}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{91E923F6-518C-3B25-9041-3D5F8A5A3589}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.Debug|x64.ActiveCfg = Debug|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.Debug|x64.Build.0 = Debug|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.Release|x64.ActiveCfg = Release|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.Release|x64.Build.0 = Release|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{657D5982-5DEA-3249-A6BB-B87DC13748E6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.Debug|x64.ActiveCfg = Debug|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.Debug|x64.Build.0 = Debug|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.Release|x64.ActiveCfg = Release|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.Release|x64.Build.0 = Release|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8E5582DE-331C-3E77-99EA-F50C68558F30}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {42D04D6C-906E-3DF0-8068-0127B691A3C0}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
