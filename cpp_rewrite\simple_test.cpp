#include <iostream>
#include <opencv2/opencv.hpp>

int main() {
    try {
        std::cout << "=== Simple Test Program ===" << std::endl;
        std::cout << "OpenCV version: " << CV_VERSION << std::endl;
        
        // 测试OpenCV
        cv::Mat testImage = cv::Mat::zeros(100, 100, CV_8UC3);
        std::cout << "OpenCV Mat created successfully" << std::endl;
        
        // 测试窗口创建
        cv::namedWindow("Test", cv::WINDOW_NORMAL);
        std::cout << "OpenCV window created successfully" << std::endl;
        
        cv::destroyAllWindows();
        std::cout << "Test completed successfully!" << std::endl;
        
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return -1;
    }
    catch (...) {
        std::cerr << "Unknown exception" << std::endl;
        return -1;
    }
}
