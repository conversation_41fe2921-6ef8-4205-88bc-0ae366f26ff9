#pragma once

#include "Common.h"

/**
 * 检测后处理类
 * 负责NMS、置信度过滤和目标选择
 */
class PostProcessor {
public:
    /**
     * 构造函数
     * @param confidenceThreshold 置信度阈值
     * @param nmsThreshold NMS阈值
     * @param numClasses 类别数量
     */
    PostProcessor(float confidenceThreshold = 0.65f, float nmsThreshold = 0.2f, int numClasses = 2);
    
    /**
     * 析构函数
     */
    ~PostProcessor() = default;

    /**
     * 处理推理结果
     * @param output 模型输出张量
     * @param inputSize 输入图像尺寸
     * @param originalSize 原始图像尺寸
     * @return 检测结果列表
     */
    std::vector<Detection> ProcessOutput(
        const std::vector<float>& output,
        const cv::Size& inputSize,
        const cv::Size& originalSize
    );

    /**
     * 选择最佳目标
     * @param detections 检测结果列表
     * @param aimPriority 瞄准优先级 (1=头部, 2=身体)
     * @param crosshair 准星位置
     * @param aimRadius 瞄准半径
     * @return 最佳目标，如果没有返回nullptr
     */
    std::shared_ptr<Detection> SelectBestTarget(
        const std::vector<Detection>& detections,
        int aimPriority,
        const cv::Point2f& crosshair,
        float aimRadius
    );

    /**
     * 在图像上绘制检测结果
     * @param image 输入图像
     * @param detections 检测结果
     * @param bestTarget 最佳目标
     * @param crosshair 准星位置
     * @param aimRadius 瞄准半径
     * @return 绘制后的图像
     */
    cv::Mat DrawDetections(
        const cv::Mat& image,
        const std::vector<Detection>& detections,
        const std::shared_ptr<Detection>& bestTarget,
        const cv::Point2f& crosshair,
        float aimRadius
    );

    // 设置参数
    void SetConfidenceThreshold(float threshold) { m_confidenceThreshold = threshold; }
    void SetNMSThreshold(float threshold) { m_nmsThreshold = threshold; }
    void SetNumClasses(int numClasses) { m_numClasses = numClasses; }
    
    // 获取参数
    float GetConfidenceThreshold() const { return m_confidenceThreshold; }
    float GetNMSThreshold() const { return m_nmsThreshold; }
    int GetNumClasses() const { return m_numClasses; }

    /**
     * 获取性能统计
     */
    PerformanceStats GetPerformanceStats() const { return m_stats; }

private:
    // 参数
    float m_confidenceThreshold;
    float m_nmsThreshold;
    int m_numClasses;
    
    // 性能统计
    mutable PerformanceStats m_stats;
    mutable TimePoint m_startTime;
    
    // 私有方法
    std::vector<Detection> ParseYOLOv8Output(
        const std::vector<float>& output,
        const cv::Size& inputSize
    );
    
    std::vector<Detection> ParseYOLOv5Output(
        const std::vector<float>& output,
        const cv::Size& inputSize
    );
    
    std::vector<Detection> ApplyNMS(const std::vector<Detection>& detections);
    
    std::vector<Detection> ScaleDetections(
        const std::vector<Detection>& detections,
        const cv::Size& inputSize,
        const cv::Size& originalSize
    );
    
    float CalculateIoU(const cv::Rect2f& box1, const cv::Rect2f& box2);
    
    void StartTimer() const;
    double EndTimer() const;
    void UpdatePerformanceStats(double processTime) const;
};
