#include "../include/MouseController.h"

// SystemMouseController 实现
ErrorCode SystemMouseController::Initialize() {
    LOG_INFO("System mouse controller initialized");
    return ErrorCode::Success;
}

bool SystemMouseController::MoveMouse(int dx, int dy) {
    if (dx == 0 && dy == 0) return true;
    
    INPUT input = {};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_MOVE;
    input.mi.dx = dx;
    input.mi.dy = dy;
    
    return SendInput(1, &input, sizeof(INPUT)) == 1;
}

bool SystemMouseController::MouseLeftDown() {
    INPUT input = {};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
    
    return SendInput(1, &input, sizeof(INPUT)) == 1;
}

bool SystemMouseController::MouseLeftUp() {
    INPUT input = {};
    input.type = INPUT_MOUSE;
    input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
    
    return SendInput(1, &input, sizeof(INPUT)) == 1;
}

bool SystemMouseController::IsKeyPressed(int keyCode) {
    return (GetAsyncKeyState(keyCode) & 0x8000) != 0;
}

void SystemMouseController::Release() {
    // 系统API不需要特殊清理
}

// KMBoxMouseController 实现
KMBoxMouseController::KMBoxMouseController(const std::string& ip, const std::string& port, const std::string& mac)
    : m_ip(ip)
    , m_port(port)
    , m_mac(mac)
    , m_initialized(false)
    , m_kmboxDll(nullptr)
    , m_initFunc(nullptr)
    , m_moveFunc(nullptr)
    , m_mouseDownFunc(nullptr)
    , m_mouseUpFunc(nullptr)
    , m_keyPressedFunc(nullptr)
    , m_cleanupFunc(nullptr)
{
}

ErrorCode KMBoxMouseController::Initialize() {
    // 加载KMBox DLL
    m_kmboxDll = LoadLibraryA("kmbox.dll");
    if (!m_kmboxDll) {
        LOG_ERROR("Failed to load kmbox.dll");
        return ErrorCode::MouseInitFailed;
    }

    // 获取函数指针
    m_initFunc = (InitKMBoxFunc)GetProcAddress(m_kmboxDll, "InitKMBox");
    m_moveFunc = (MoveMouseFunc)GetProcAddress(m_kmboxDll, "MoveMouse");
    m_mouseDownFunc = (MouseDownFunc)GetProcAddress(m_kmboxDll, "MouseDown");
    m_mouseUpFunc = (MouseUpFunc)GetProcAddress(m_kmboxDll, "MouseUp");
    m_keyPressedFunc = (IsKeyPressedFunc)GetProcAddress(m_kmboxDll, "IsKeyPressed");
    m_cleanupFunc = (CleanupFunc)GetProcAddress(m_kmboxDll, "Cleanup");

    if (!m_initFunc || !m_moveFunc) {
        LOG_ERROR("Failed to get KMBox function pointers");
        FreeLibrary(m_kmboxDll);
        return ErrorCode::MouseInitFailed;
    }

    // 初始化KMBox
    if (!m_initFunc(m_ip.c_str(), m_port.c_str(), m_mac.c_str())) {
        LOG_ERROR("Failed to initialize KMBox connection");
        FreeLibrary(m_kmboxDll);
        return ErrorCode::MouseInitFailed;
    }

    m_initialized = true;
    LOG_INFO("KMBox mouse controller initialized: " << m_ip << ":" << m_port);
    return ErrorCode::Success;
}

bool KMBoxMouseController::MoveMouse(int dx, int dy) {
    if (!m_initialized || !m_moveFunc) return false;
    return m_moveFunc(dx, dy);
}

bool KMBoxMouseController::MouseLeftDown() {
    if (!m_initialized || !m_mouseDownFunc) return false;
    return m_mouseDownFunc();
}

bool KMBoxMouseController::MouseLeftUp() {
    if (!m_initialized || !m_mouseUpFunc) return false;
    return m_mouseUpFunc();
}

bool KMBoxMouseController::IsKeyPressed(int keyCode) {
    if (!m_initialized || !m_keyPressedFunc) {
        return (GetAsyncKeyState(keyCode) & 0x8000) != 0; // 回退到系统API
    }
    return m_keyPressedFunc(keyCode);
}

void KMBoxMouseController::Release() {
    if (m_cleanupFunc) {
        m_cleanupFunc();
    }
    if (m_kmboxDll) {
        FreeLibrary(m_kmboxDll);
        m_kmboxDll = nullptr;
    }
    m_initialized = false;
}

// GHubMouseController 实现
ErrorCode GHubMouseController::Initialize() {
    // 加载GHub DLL
    m_ghubDll = LoadLibraryA("ghub.dll");
    if (!m_ghubDll) {
        LOG_ERROR("Failed to load ghub.dll");
        return ErrorCode::MouseInitFailed;
    }

    // 获取函数指针
    m_moveFunc = (GHubMoveFunc)GetProcAddress(m_ghubDll, "GHubMoveMouse");
    m_mouseDownFunc = (GHubMouseDownFunc)GetProcAddress(m_ghubDll, "GHubMouseDown");
    m_mouseUpFunc = (GHubMouseUpFunc)GetProcAddress(m_ghubDll, "GHubMouseUp");

    if (!m_moveFunc) {
        LOG_ERROR("Failed to get GHub function pointers");
        FreeLibrary(m_ghubDll);
        return ErrorCode::MouseInitFailed;
    }

    m_initialized = true;
    LOG_INFO("GHub mouse controller initialized");
    return ErrorCode::Success;
}

bool GHubMouseController::MoveMouse(int dx, int dy) {
    if (!m_initialized || !m_moveFunc) return false;
    return m_moveFunc(dx, dy);
}

bool GHubMouseController::MouseLeftDown() {
    if (!m_initialized || !m_mouseDownFunc) return false;
    return m_mouseDownFunc();
}

bool GHubMouseController::MouseLeftUp() {
    if (!m_initialized || !m_mouseUpFunc) return false;
    return m_mouseUpFunc();
}

bool GHubMouseController::IsKeyPressed(int keyCode) {
    return (GetAsyncKeyState(keyCode) & 0x8000) != 0;
}

void GHubMouseController::Release() {
    if (m_ghubDll) {
        FreeLibrary(m_ghubDll);
        m_ghubDll = nullptr;
    }
    m_initialized = false;
}

// MouseControllerFactory 实现
std::unique_ptr<MouseController> MouseControllerFactory::CreateMouseController(
    const std::string& type,
    const MouseConfig& config
) {
    if (type == "system") {
        return std::make_unique<SystemMouseController>();
    }
    else if (type == "kmbox") {
        return std::make_unique<KMBoxMouseController>(config.kmboxIP, config.kmboxPort, config.kmboxMAC);
    }
    else if (type == "ghub") {
        return std::make_unique<GHubMouseController>();
    }
    else {
        LOG_ERROR("Unknown mouse controller type: " << type);
        return nullptr;
    }
}
