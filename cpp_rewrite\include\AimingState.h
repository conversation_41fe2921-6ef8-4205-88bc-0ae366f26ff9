#pragma once

#include "Common.h"

/**
 * 瞄准状态管理器
 * 负责线程间通信和状态同步
 */
class AimingState {
public:
    /**
     * 构造函数
     */
    AimingState();
    
    /**
     * 析构函数
     */
    ~AimingState() = default;

    /**
     * 更新瞄准状态
     * @param isAiming 是否正在瞄准
     */
    void UpdateAimStatus(bool isAiming);

    /**
     * 更新目标信息
     * @param target 目标检测结果
     * @param crosshair 准星位置
     * @param offsetY Y轴偏移
     */
    void UpdateTarget(const std::shared_ptr<Detection>& target, const cv::Point2f& crosshair, float offsetY);

    /**
     * 获取控制信息
     * @param isAiming 输出：是否正在瞄准
     * @param totalError 输出：总误差
     * @param targetInfo 输出：目标信息
     * @param newTargetAvailable 输出：是否有新目标
     * @return 是否有有效数据
     */
    bool GetControlInfo(bool& isAiming, cv::Point2f& totalError, std::shared_ptr<Detection>& targetInfo, bool& newTargetAvailable);

    /**
     * 请求停止程序
     */
    void RequestStop();

    /**
     * 检查程序是否应该继续运行
     */
    bool IsRunning() const;

    /**
     * 更新性能统计
     * @param stats 性能统计数据
     */
    void UpdatePerformanceStats(const PerformanceStats& stats);

    /**
     * 获取性能统计
     */
    PerformanceStats GetPerformanceStats() const;

    /**
     * 设置调试模式
     * @param enable 是否启用调试输出
     */
    void SetDebugMode(bool enable) { m_debugMode = enable; }

    /**
     * 获取调试信息
     */
    struct DebugInfo {
        bool isAiming;
        cv::Point2f totalError;
        cv::Point2f targetCenter;
        float targetConfidence;
        int targetClass;
        TimePoint lastUpdateTime;
        int frameCount;
    };
    
    DebugInfo GetDebugInfo() const;

private:
    // 线程同步
    mutable std::mutex m_mutex;
    std::atomic<bool> m_running;
    std::atomic<bool> m_debugMode;
    
    // 瞄准状态
    bool m_isAiming;
    bool m_newTargetAvailable;
    
    // 目标信息
    std::shared_ptr<Detection> m_currentTarget;
    cv::Point2f m_crosshair;
    cv::Point2f m_totalError;
    float m_offsetY;
    
    // 性能统计
    PerformanceStats m_performanceStats;
    
    // 调试信息
    DebugInfo m_debugInfo;
    TimePoint m_lastUpdateTime;
    int m_frameCount;
    
    // 私有方法
    cv::Point2f CalculateTargetPoint(const Detection& target, float offsetY) const;
    void UpdateDebugInfo();
};
