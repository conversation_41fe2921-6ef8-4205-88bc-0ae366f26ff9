^C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\BUILD_INFERENCE\CMAKEFILES\D1649F103DD34DDFE1B23F256EEF714E\GENERATE.STAMP.RULE
setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite -BC:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/foye_111111/cpp_rewrite/build_inference/AimAssistWithInference.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
