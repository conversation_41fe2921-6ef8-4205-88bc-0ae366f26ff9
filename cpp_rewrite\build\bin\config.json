# AimAssist Configuration File
# This is a simple key=value format configuration

# Model Configuration
model_path=models/PUBGV8_320.onnx
model_type=yolov8
confidence_threshold=0.65
nms_threshold=0.2
input_size=320
num_classes=2

# Capture Configuration
capture_method=screen
screen_width=1920
screen_height=1080
roi_size=640

# Mouse Configuration
mouse_mode=system
kmbox_ip=*************
kmbox_port=8808
kmbox_mac=62587019

# Aim Configuration
aim_circle_radius=90
aim_key1=5
aim_key2=1
offset_y1=0.3
offset_y2=0.4

# Display Configuration
show_preview=true
font_path=C:/Windows/Fonts/simhei.ttf
font_size=20

# PID Parameters - System Mode
pid_system_kp=50.0
pid_system_ki=0.0
pid_system_kd=0.005
pid_system_base_ki=0.0
pid_system_max_ki=80.0
pid_system_inner_threshold=20.0
pid_system_outer_threshold=50.0
pid_system_control_frequency=500

# PID Parameters - KMBox Mode
pid_kmbox_kp=50.0
pid_kmbox_ki=0.0
pid_kmbox_kd=0.05
pid_kmbox_base_ki=0.0
pid_kmbox_max_ki=80.0
pid_kmbox_inner_threshold=50.0
pid_kmbox_outer_threshold=100.0
pid_kmbox_control_frequency=500

# PID Parameters - GHub Mode
pid_ghub_kp=50.0
pid_ghub_ki=0.0
pid_ghub_kd=0.005
pid_ghub_base_ki=0.0
pid_ghub_max_ki=80.0
pid_ghub_inner_threshold=20.0
pid_ghub_outer_threshold=50.0
pid_ghub_control_frequency=500
