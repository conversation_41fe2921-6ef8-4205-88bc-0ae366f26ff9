import time
import numpy as np

class PIDController:
    """
    PID控制器，用于平滑自瞄移动
    """
    def __init__(self, kp=0.6, ki=0.0, kd=0.0, windup_guard=10.0):
        """
        初始化PID控制器
        
        参数:
            kp (float): 比例增益，控制响应强度
            ki (float): 积分增益，减少稳态误差
            kd (float): 微分增益，提供阻尼减少过冲
            windup_guard (float): 积分项限制，防止积分饱和
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.windup_guard = windup_guard
        
        # 初始化状态变量
        self.last_error = np.array([0.0, 0.0])  # X和Y方向的上一次误差
        self.integral = np.array([0.0, 0.0])    # 误差积分项
        self.last_time = time.perf_counter()    # 上一次更新时间
        
    def reset(self):
        """重置PID控制器状态"""
        self.last_error = np.array([0.0, 0.0])
        self.integral = np.array([0.0, 0.0])
        self.last_time = time.perf_counter()
        
    def compute(self, error, current_time=None):
        """
        计算PID控制输出
        
        参数:
            error (numpy.ndarray): [dx, dy] 当前误差向量
            current_time (float): 当前时间戳，如果为None则自动获取
            
        返回:
            numpy.ndarray: PID控制输出 [move_dx, move_dy]
        """
        if current_time is None:
            current_time = time.perf_counter()
            
        # 计算时间差
        dt = current_time - self.last_time
        if dt <= 0:
            dt = 0.001  # 防止除零错误
            
        # 将误差转换为numpy数组
        error = np.array(error, dtype=np.float64)
        
        # 计算误差导数 (变化率)
        delta_error = (error - self.last_error) / dt
        
        # 更新积分项
        self.integral += error * dt
        
        # 积分限制，防止积分饱和
        for i in range(len(self.integral)):
            if self.integral[i] > self.windup_guard:
                self.integral[i] = self.windup_guard
            elif self.integral[i] < -self.windup_guard:
                self.integral[i] = -self.windup_guard
                
        # 计算PID输出
        output = self.kp * error + self.ki * self.integral + self.kd * delta_error
        
        # 更新状态
        self.last_error = error.copy()
        self.last_time = current_time
        
        return output
        
    def set_gains(self, kp=None, ki=None, kd=None):
        """更新PID增益参数"""
        if kp is not None:
            self.kp = kp
        if ki is not None:
            self.ki = ki
        if kd is not None:
            self.kd = kd
            
    def get_gains(self):
        """获取当前PID增益参数"""
        return {"kp": self.kp, "ki": self.ki, "kd": self.kd} 