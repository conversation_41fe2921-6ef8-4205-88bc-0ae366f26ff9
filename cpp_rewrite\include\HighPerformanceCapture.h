#pragma once

#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <memory>
#include <vector>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <queue>
#include <chrono>

#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

/**
 * 高性能帧缓冲区
 * 使用内存池避免频繁分配
 */
struct FrameBuffer {
    std::vector<uint8_t> data;
    int width;
    int height;
    int channels;
    std::chrono::high_resolution_clock::time_point timestamp;
    bool inUse;
    
    FrameBuffer() : width(0), height(0), channels(0), inUse(false) {}
    
    void Resize(int w, int h, int c) {
        if (width != w || height != h || channels != c) {
            width = w;
            height = h;
            channels = c;
            data.resize(w * h * c);
        }
    }
    
    uint8_t* GetData() { return data.data(); }
    size_t GetSize() const { return data.size(); }
};

/**
 * 内存池管理器
 */
class FrameBufferPool {
private:
    std::vector<std::unique_ptr<FrameBuffer>> m_buffers;
    std::mutex m_mutex;
    size_t m_poolSize;
    
public:
    FrameBufferPool(size_t poolSize = 10) : m_poolSize(poolSize) {
        for (size_t i = 0; i < poolSize; ++i) {
            m_buffers.push_back(std::make_unique<FrameBuffer>());
        }
    }
    
    FrameBuffer* AcquireBuffer() {
        std::lock_guard<std::mutex> lock(m_mutex);
        for (auto& buffer : m_buffers) {
            if (!buffer->inUse) {
                buffer->inUse = true;
                return buffer.get();
            }
        }
        // 如果没有可用缓冲区，创建新的
        auto newBuffer = std::make_unique<FrameBuffer>();
        newBuffer->inUse = true;
        FrameBuffer* ptr = newBuffer.get();
        m_buffers.push_back(std::move(newBuffer));
        return ptr;
    }
    
    void ReleaseBuffer(FrameBuffer* buffer) {
        if (buffer) {
            std::lock_guard<std::mutex> lock(m_mutex);
            buffer->inUse = false;
        }
    }
};

/**
 * 性能统计器
 */
class PerformanceCounter {
public:
    std::atomic<int> captureCount{0};
    std::atomic<int> processCount{0};
    std::atomic<double> avgCaptureTime{0.0};
    std::atomic<double> avgProcessTime{0.0};
    std::chrono::high_resolution_clock::time_point startTime;

    PerformanceCounter() {
        startTime = std::chrono::high_resolution_clock::now();
    }

    // 禁用拷贝构造和赋值
    PerformanceCounter(const PerformanceCounter&) = delete;
    PerformanceCounter& operator=(const PerformanceCounter&) = delete;

    // 允许移动
    PerformanceCounter(PerformanceCounter&& other) noexcept
        : captureCount(other.captureCount.load())
        , processCount(other.processCount.load())
        , avgCaptureTime(other.avgCaptureTime.load())
        , avgProcessTime(other.avgProcessTime.load())
        , startTime(other.startTime)
    {
    }

    double GetFPS() const {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime);
        return captureCount.load() * 1000.0 / duration.count();
    }

    void Reset() {
        captureCount = 0;
        processCount = 0;
        avgCaptureTime = 0.0;
        avgProcessTime = 0.0;
        startTime = std::chrono::high_resolution_clock::now();
    }
};

/**
 * 高性能屏幕截图类
 */
class HighPerformanceCapture {
public:
    struct Config {
        int roiX = -1;
        int roiY = -1;
        int roiWidth = 640;
        int roiHeight = 640;
        int targetWidth = 320;
        int targetHeight = 320;
        bool enableAsync = true;
        bool enableGPUProcessing = true;
        int bufferPoolSize = 10;
        int maxQueueSize = 5;
    };

private:
    // DXGI相关
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    // GPU处理相关
    ID3D11Texture2D* m_pProcessingTexture;
    ID3D11ShaderResourceView* m_pSRV;
    ID3D11RenderTargetView* m_pRTV;
    
    // 配置和状态
    Config m_config;
    int m_screenWidth, m_screenHeight;
    bool m_initialized;
    std::atomic<bool> m_running;
    
    // 内存管理
    std::unique_ptr<FrameBufferPool> m_bufferPool;
    
    // 异步处理
    std::unique_ptr<std::thread> m_captureThread;
    std::unique_ptr<std::thread> m_processThread;
    
    // 线程同步
    std::queue<FrameBuffer*> m_captureQueue;
    std::queue<FrameBuffer*> m_processedQueue;
    std::mutex m_captureMutex;
    std::mutex m_processedMutex;
    std::condition_variable m_captureCV;
    std::condition_variable m_processedCV;
    
    // 性能统计
    PerformanceCounter m_perfCounter;

public:
    HighPerformanceCapture(const Config& config = Config());
    ~HighPerformanceCapture();
    
    bool Initialize();
    void Release();
    
    // 同步接口
    cv::Mat CaptureFrame();
    
    // 异步接口
    bool StartAsync();
    void StopAsync();
    cv::Mat GetProcessedFrame(int timeoutMs = 1);
    
    // 性能监控
    const PerformanceCounter& GetPerformanceStats() const { return m_perfCounter; }
    void ResetPerformanceStats() { m_perfCounter.Reset(); }
    
    // 配置管理
    void UpdateConfig(const Config& config);
    Config GetConfig() const { return m_config; }

private:
    // 初始化方法
    bool InitializeDXGI();
    bool InitializeGPUProcessing();
    bool CreateStagingTexture();
    
    // 核心捕获方法
    bool CaptureRawFrame(FrameBuffer* buffer);
    bool ProcessFrame(FrameBuffer* input, FrameBuffer* output);
    
    // GPU处理方法
    bool ProcessFrameGPU(FrameBuffer* input, FrameBuffer* output);
    bool ProcessFrameCPU(FrameBuffer* input, FrameBuffer* output);
    
    // 线程函数
    void CaptureThreadFunc();
    void ProcessThreadFunc();
    
    // 工具方法
    void ApplyROI(FrameBuffer* buffer);
    void ConvertBGRAToRGB(FrameBuffer* buffer);
    void ResizeFrame(FrameBuffer* input, FrameBuffer* output);
    
    // 性能测量
    void UpdateCaptureStats(double captureTime);
    void UpdateProcessStats(double processTime);
};

/**
 * 高性能截图工厂
 */
class HighPerformanceCaptureFactory {
public:
    static std::unique_ptr<HighPerformanceCapture> CreateCapture(
        const HighPerformanceCapture::Config& config = HighPerformanceCapture::Config());
    
    static bool TestSystemCapabilities();
    static HighPerformanceCapture::Config GetOptimalConfig();
};
