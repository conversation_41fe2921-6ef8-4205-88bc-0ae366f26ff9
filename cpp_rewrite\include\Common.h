#pragma once

#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <vector>
#include <memory>
#include <string>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>
#include <iostream>

// 安全释放宏
#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

// 日志宏
#define LOG_INFO(msg) std::cout << "[INFO] " << msg << std::endl
#define LOG_ERROR(msg) std::cerr << "[ERROR] " << msg << std::endl
#define LOG_DEBUG(msg) std::cout << "[DEBUG] " << msg << std::endl

// 常用类型定义
using TimePoint = std::chrono::high_resolution_clock::time_point;
using Duration = std::chrono::duration<double, std::milli>;

// 检测结果结构体
struct Detection {
    cv::Rect2f box;        // 边界框 (x, y, width, height)
    float confidence;      // 置信度
    int classId;          // 类别ID
    cv::Point2f center;   // 中心点
    
    Detection() : confidence(0.0f), classId(-1) {}
    
    Detection(const cv::Rect2f& b, float conf, int cls) 
        : box(b), confidence(conf), classId(cls) {
        center = cv::Point2f(box.x + box.width * 0.5f, box.y + box.height * 0.5f);
    }
};

// 性能统计结构体
struct PerformanceStats {
    double captureTimeMs = 0.0;      // 截图耗时
    double inferenceTimeMs = 0.0;    // 推理耗时
    double postprocessTimeMs = 0.0;  // 后处理耗时
    double controlTimeMs = 0.0;      // 控制耗时
    double totalTimeMs = 0.0;        // 总耗时
    int frameCount = 0;              // 帧计数
    double avgFps = 0.0;             // 平均FPS
    
    void Reset() {
        captureTimeMs = inferenceTimeMs = postprocessTimeMs = controlTimeMs = totalTimeMs = avgFps = 0.0;
        frameCount = 0;
    }
    
    void UpdateFPS() {
        if (totalTimeMs > 0) {
            avgFps = 1000.0 / totalTimeMs;
        }
    }
};

// 瞄准配置结构体
struct AimConfig {
    int aimKey1 = 0x05;              // 瞄准键1 (鼠标侧键)
    int aimKey2 = 0x01;              // 瞄准键2 (鼠标左键)
    float offsetY1 = 0.3f;           // 瞄准偏移1
    float offsetY2 = 0.4f;           // 瞄准偏移2
    int aimCircleRadius = 90;        // 瞄准圆半径
    bool enableTrigger = false;      // 是否启用扳机
    int triggerDelay = 30;           // 扳机延迟(ms)
    int triggerInterval = 60;        // 扳机间隔(ms)
};

// PID参数结构体
struct PIDParams {
    float kp = 50.0f;                // 比例系数
    float ki = 0.0f;                 // 积分系数
    float kd = 0.005f;               // 微分系数
    float baseKi = 0.0f;             // 基础积分系数
    float maxKi = 80.0f;             // 最大积分系数
    float innerThreshold = 20.0f;    // 内圈阈值
    float outerThreshold = 50.0f;    // 外圈阈值
    int controlFrequency = 500;      // 控制频率(Hz)
};

// 模型配置结构体
struct ModelConfig {
    std::string modelPath = "models/PUBGV8_320.onnx";
    float confidenceThreshold = 0.65f;
    float nmsThreshold = 0.2f;
    std::string modelType = "yolov8";
    int numClasses = 2;
    std::vector<std::string> classNames = {"head", "body"};
    int inputSize = 320;
};

// 截图配置结构体
struct CaptureConfig {
    std::string method = "screen";    // 截图方式: screen/udp
    int screenWidth = 1920;
    int screenHeight = 1080;
    int roiSize = 640;
    std::string udpHost = "127.0.0.1";
    int udpPort = 8000;
    int bufferSize = 65536;
};

// 鼠标控制配置结构体
struct MouseConfig {
    std::string moveMode = "system";  // 移动方式: system/kmbox/ghub
    std::string kmboxIP = "*************";
    std::string kmboxPort = "8808";
    std::string kmboxMAC = "62587019";
    std::string kmboxMoveType = "normal";
};

// 显示配置结构体
struct DisplayConfig {
    std::string fontPath = "C:/Windows/Fonts/simhei.ttf";
    int fontSize = 20;
    bool showPreview = true;
    bool showTiming = false;
};

// 工具函数
namespace Utils {
    // 获取当前时间点
    inline TimePoint GetCurrentTime() {
        return std::chrono::high_resolution_clock::now();
    }
    
    // 计算时间差(毫秒)
    inline double GetElapsedTime(const TimePoint& start) {
        auto end = GetCurrentTime();
        return std::chrono::duration<double, std::milli>(end - start).count();
    }
    
    // 计算两点距离
    inline float Distance(const cv::Point2f& p1, const cv::Point2f& p2) {
        float dx = p1.x - p2.x;
        float dy = p1.y - p2.y;
        return std::sqrt(dx * dx + dy * dy);
    }
    
    // 限制值在范围内
    template<typename T>
    inline T Clamp(T value, T min, T max) {
        return std::max(min, std::min(value, max));
    }
    
    // 检查按键是否按下
    inline bool IsKeyPressed(int keyCode) {
        return (GetAsyncKeyState(keyCode) & 0x8000) != 0;
    }
}

// 错误码定义
enum class ErrorCode {
    Success = 0,
    InitializationFailed,
    ModelLoadFailed,
    CaptureInitFailed,
    InferenceFailed,
    ConfigLoadFailed,
    MouseInitFailed
};

// 错误信息转换
inline std::string ErrorCodeToString(ErrorCode code) {
    switch (code) {
        case ErrorCode::Success: return "Success";
        case ErrorCode::InitializationFailed: return "Initialization Failed";
        case ErrorCode::ModelLoadFailed: return "Model Load Failed";
        case ErrorCode::CaptureInitFailed: return "Capture Init Failed";
        case ErrorCode::InferenceFailed: return "Inference Failed";
        case ErrorCode::ConfigLoadFailed: return "Config Load Failed";
        case ErrorCode::MouseInitFailed: return "Mouse Init Failed";
        default: return "Unknown Error";
    }
}
