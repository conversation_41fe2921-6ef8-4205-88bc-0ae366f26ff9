#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <iostream>
#include <vector>
#include <chrono>

// 安全释放宏
#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

class SimpleScreenCapture {
private:
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    int m_screenWidth;
    int m_screenHeight;
    bool m_initialized;

public:
    SimpleScreenCapture() 
        : m_pDevice(nullptr)
        , m_pContext(nullptr)
        , m_pDeskDupl(nullptr)
        , m_pStagingTexture(nullptr)
        , m_screenWidth(0)
        , m_screenHeight(0)
        , m_initialized(false)
    {
    }

    ~SimpleScreenCapture() {
        Release();
    }

    bool Initialize() {
        if (m_initialized) {
            return true;
        }

        // 获取屏幕尺寸
        m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
        m_screenHeight = GetSystemMetrics(SM_CYSCREEN);
        
        std::cout << "Screen size: " << m_screenWidth << "x" << m_screenHeight << std::endl;

        // 初始化DXGI
        if (!InitializeDXGI()) {
            std::cerr << "Failed to initialize DXGI" << std::endl;
            return false;
        }

        m_initialized = true;
        std::cout << "Screen capture initialized successfully" << std::endl;
        return true;
    }

    cv::Mat CaptureScreen() {
        if (!m_initialized) {
            return cv::Mat();
        }

        std::vector<BYTE> frameBuffer;
        int width, height;
        
        HRESULT hr = GetNextFrame(frameBuffer, width, height);
        if (FAILED(hr) || hr == S_FALSE) {
            return cv::Mat();
        }

        // 创建OpenCV Mat (BGRA格式)
        cv::Mat bgraFrame(height, width, CV_8UC4, frameBuffer.data());
        
        // 转换为BGR格式用于显示
        cv::Mat bgrFrame;
        cv::cvtColor(bgraFrame, bgrFrame, cv::COLOR_BGRA2BGR);

        return bgrFrame.clone();
    }

    void Release() {
        SAFE_RELEASE(m_pStagingTexture);
        SAFE_RELEASE(m_pDeskDupl);
        SAFE_RELEASE(m_pContext);
        SAFE_RELEASE(m_pDevice);
        
        m_initialized = false;
        std::cout << "Screen capture resources released" << std::endl;
    }

private:
    bool InitializeDXGI() {
        HRESULT hr = S_OK;

        // 1. 创建D3D11设备
        D3D_FEATURE_LEVEL featureLevel;
        hr = D3D11CreateDevice(
            nullptr,
            D3D_DRIVER_TYPE_HARDWARE,
            nullptr,
            0,
            nullptr,
            0,
            D3D11_SDK_VERSION,
            &m_pDevice,
            &featureLevel,
            &m_pContext
        );

        if (FAILED(hr)) {
            std::cerr << "Failed to create D3D11 device: 0x" << std::hex << hr << std::endl;
            return false;
        }

        // 2. 获取DXGI设备
        IDXGIDevice* pDxgiDevice = nullptr;
        hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
        if (FAILED(hr)) {
            std::cerr << "Failed to get DXGI device: 0x" << std::hex << hr << std::endl;
            return false;
        }

        // 3. 获取DXGI适配器
        IDXGIAdapter* pDxgiAdapter = nullptr;
        hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
        SAFE_RELEASE(pDxgiDevice);
        if (FAILED(hr)) {
            std::cerr << "Failed to get DXGI adapter: 0x" << std::hex << hr << std::endl;
            return false;
        }

        // 4. 获取输出
        IDXGIOutput* pDxgiOutput = nullptr;
        hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
        SAFE_RELEASE(pDxgiAdapter);
        if (FAILED(hr)) {
            std::cerr << "Failed to enumerate outputs: 0x" << std::hex << hr << std::endl;
            return false;
        }

        // 5. 获取输出1接口
        IDXGIOutput1* pDxgiOutput1 = nullptr;
        hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
        SAFE_RELEASE(pDxgiOutput);
        if (FAILED(hr)) {
            std::cerr << "Failed to get IDXGIOutput1: 0x" << std::hex << hr << std::endl;
            return false;
        }

        // 6. 创建桌面复制
        hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
        SAFE_RELEASE(pDxgiOutput1);
        if (FAILED(hr)) {
            std::cerr << "Failed to duplicate output: 0x" << std::hex << hr << std::endl;
            return false;
        }

        std::cout << "DXGI initialized successfully" << std::endl;
        return true;
    }

    bool CreateStagingTexture() {
        DXGI_OUTDUPL_DESC duplDesc;
        m_pDeskDupl->GetDesc(&duplDesc);

        D3D11_TEXTURE2D_DESC desc = {};
        desc.Width = duplDesc.ModeDesc.Width;
        desc.Height = duplDesc.ModeDesc.Height;
        desc.MipLevels = 1;
        desc.ArraySize = 1;
        desc.Format = duplDesc.ModeDesc.Format;
        desc.SampleDesc.Count = 1;
        desc.SampleDesc.Quality = 0;
        desc.Usage = D3D11_USAGE_STAGING;
        desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        desc.BindFlags = 0;
        desc.MiscFlags = 0;

        HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
        if (FAILED(hr)) {
            std::cerr << "Failed to create staging texture: 0x" << std::hex << hr << std::endl;
            return false;
        }

        return true;
    }

    HRESULT GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height) {
        if (!m_pDeskDupl) {
            return E_FAIL;
        }

        HRESULT hr;
        IDXGIResource* pDesktopResource = nullptr;
        DXGI_OUTDUPL_FRAME_INFO frameInfo;

        // 获取下一帧
        hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);
        
        if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
            return S_FALSE; // 没有新帧
        }
        if (FAILED(hr)) {
            return hr;
        }

        // 转换为纹理
        ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
        hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
        SAFE_RELEASE(pDesktopResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 创建中转纹理
        if (!m_pStagingTexture) {
            if (!CreateStagingTexture()) {
                SAFE_RELEASE(pAcquiredDesktopImage);
                m_pDeskDupl->ReleaseFrame();
                return E_FAIL;
            }
        }

        // 复制到中转纹理
        m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
        SAFE_RELEASE(pAcquiredDesktopImage);

        // 映射纹理数据
        D3D11_MAPPED_SUBRESOURCE mappedResource;
        hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 复制数据到缓冲区
        const UINT rowPitch = mappedResource.RowPitch;
        const UINT framePitch = m_screenWidth * 4; // BGRA格式
        frameBuffer.resize(framePitch * m_screenHeight);

        BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
        BYTE* pDest = frameBuffer.data();
        for (int y = 0; y < m_screenHeight; ++y) {
            memcpy(pDest, pSource, framePitch);
            pSource += rowPitch;
            pDest += framePitch;
        }

        // 解除映射并释放帧
        m_pContext->Unmap(m_pStagingTexture, 0);
        m_pDeskDupl->ReleaseFrame();

        width = m_screenWidth;
        height = m_screenHeight;

        return S_OK;
    }
};

int main() {
    std::cout << "=== Simple Screen Capture Test ===" << std::endl;

    SimpleScreenCapture capture;
    
    if (!capture.Initialize()) {
        std::cerr << "Failed to initialize screen capture" << std::endl;
        return -1;
    }

    // 创建显示窗口
    cv::namedWindow("Screen Capture Test", cv::WINDOW_NORMAL);
    cv::resizeWindow("Screen Capture Test", 800, 600);

    std::cout << "Starting capture loop. Press 'q' to quit, 's' to save screenshot." << std::endl;

    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();

    while (true) {
        cv::Mat frame = capture.CaptureScreen();
        
        if (frame.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        frameCount++;

        // 缩放显示
        cv::Mat displayFrame;
        cv::resize(frame, displayFrame, cv::Size(800, 600));

        // 显示FPS
        if (frameCount % 30 == 0) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            double fps = frameCount * 1000.0 / duration.count();
            
            std::cout << "Frame " << frameCount << ", FPS: " << fps << std::endl;
        }

        // 在图像上显示信息
        std::string info = "Frame: " + std::to_string(frameCount);
        cv::putText(displayFrame, info, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 255, 0), 2);

        cv::imshow("Screen Capture Test", displayFrame);

        // 处理按键
        int key = cv::waitKey(1) & 0xFF;
        if (key == 'q' || key == 27) { // 'q' 或 ESC
            break;
        } else if (key == 's') { // 保存截图
            std::string filename = "screenshot_" + std::to_string(frameCount) + ".jpg";
            cv::imwrite(filename, frame);
            std::cout << "Screenshot saved: " << filename << std::endl;
        }
    }

    cv::destroyAllWindows();
    std::cout << "Test completed!" << std::endl;

    return 0;
}
