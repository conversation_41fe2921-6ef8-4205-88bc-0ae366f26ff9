{"model": {"path": "C:/Users/<USER>/Desktop/foye_111111/dist/自瞄程序/models/PUBGV8_320.onnx", "confidence_threshold": 0.65, "nms_threshold": 0.2, "model_type": "yolov8", "num_classes": 0, "class_names": []}, "capture": {"method": "screen", "screen_resolution": [1920, 1080], "roi_size": 640, "udp": {"host": "127.0.0.1", "port": 8000, "buffer_size": 65536}}, "mouse": {"move_mode": "kmbox", "kmbox": {"ip": "*************", "port": "8808", "mac": "62587019", "move_type": "normal"}, "aim_keys": {"priority_2": [{"key": 5, "offset_y": 0.1, "trigger": {"enabled": false}}, {"key": 6, "offset_y": 0.1, "trigger": {"enabled": true, "fire_delay_ms": 75, "fire_interval_ms": 52}}], "priority_1": [{"key": 2, "offset_y": 0.3, "trigger": {"enabled": false, "fire_delay_ms": 30, "fire_interval_ms": 60}}]}}, "aim_circle_radius": 90, "pid": {"system": {"kp": 50.0, "ki": 0.0, "kd": 0.005, "base_ki": 0.0, "max_ki": 80.0, "inner_threshold": 20.0, "outer_threshold": 50.0}, "kmbox": {"kp": 200.0, "ki": 0.0, "kd": 0.01, "base_ki": 20.0, "max_ki": 80.0, "inner_threshold": 20.0, "outer_threshold": 60.0}, "ghub": {"kp": 50.0, "ki": 0.0, "kd": 0.005, "base_ki": 0.0, "max_ki": 30.0, "inner_threshold": 20.0, "outer_threshold": 50.0}, "control_frequency": 500}, "display": {"font_path": "C:/Windows/Fonts/simhei.ttf", "font_size": 20, "show_preview": true}, "debug": {"show_timing": false}}