#include "../include/ScreenCapture.h"
#include <iostream>
#include <chrono>
#include <algorithm>

ScreenCapture::ScreenCapture(int roiX, int roiY, int roiWidth, int roiHeight, int targetSize)
    : m_pDevice(nullptr)
    , m_pContext(nullptr)
    , m_pDeskDupl(nullptr)
    , m_pStagingTexture(nullptr)
    , m_roiX(roiX)
    , m_roiY(roiY)
    , m_roiWidth(roiWidth)
    , m_roiHeight(roiHeight)
    , m_targetSize(targetSize)
    , m_useROI(roiX >= 0 && roiY >= 0 && roiWidth > 0 && roiHeight > 0)
    , m_initialized(false)
    , m_screenWidth(0)
    , m_screenHeight(0)
{
    QueryPerformanceFrequency(&m_frequency);
    memset(&m_stats, 0, sizeof(m_stats));
}

ScreenCapture::~ScreenCapture() {
    Release();
}

bool ScreenCapture::Initialize() {
    if (m_initialized) {
        return true;
    }

    m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
    m_screenHeight = GetSystemMetrics(SM_CYSCREEN);

    if (!m_useROI) {
        int defaultSize = 640;
        m_roiX = (m_screenWidth - defaultSize) / 2;
        m_roiY = (m_screenHeight - defaultSize) / 2;
        m_roiWidth = defaultSize;
        m_roiHeight = defaultSize;
        m_useROI = true;
    }

    if (!InitializeDXGI()) {
        std::cerr << "Failed to initialize DXGI" << std::endl;
        return false;
    }

    PreallocateBuffers();

    m_initialized = true;
    std::cout << "ScreenCapture initialized successfully" << std::endl;

    return true;
}

cv::Mat ScreenCapture::CaptureAndPreprocess() {
    if (!m_initialized) {
        return cv::Mat();
    }

    StartTimer();

    std::vector<BYTE> frameBuffer;
    int width, height;
    HRESULT hr = GetNextFrame(frameBuffer, width, height);

    double captureTime = EndTimer();

    if (FAILED(hr)) {
        return cv::Mat();
    }

    if (hr == S_FALSE) {
        return cv::Mat();
    }

    StartTimer();

    cv::Mat bgraFrame(height, width, CV_8UC4, frameBuffer.data());

    cv::Rect roi(m_roiX, m_roiY, m_roiWidth, m_roiHeight);
    
    roi.x = std::max(0, std::min(roi.x, width - 1));
    roi.y = std::max(0, std::min(roi.y, height - 1));
    roi.width = std::min(roi.width, width - roi.x);
    roi.height = std::min(roi.height, height - roi.y);
    
    cv::Mat roiFrame = bgraFrame(roi);

    cv::Mat rgbBuffer;
    cv::cvtColor(roiFrame, rgbBuffer, cv::COLOR_BGRA2RGB);

    cv::Mat resizedBuffer;
    cv::resize(rgbBuffer, resizedBuffer, cv::Size(m_targetSize, m_targetSize), 0, 0, cv::INTER_LINEAR);

    double preprocessTime = EndTimer();

    UpdatePerformanceStats(captureTime, preprocessTime);

    return resizedBuffer.clone();
}

cv::Mat ScreenCapture::CaptureRaw() {
    if (!m_initialized) {
        return cv::Mat();
    }

    std::vector<BYTE> frameBuffer;
    int width, height;
    HRESULT hr = GetNextFrame(frameBuffer, width, height);

    if (FAILED(hr) || hr == S_FALSE) {
        return cv::Mat();
    }

    return cv::Mat(height, width, CV_8UC4, frameBuffer.data()).clone();
}

void ScreenCapture::Release() {
    if (m_pStagingTexture) { m_pStagingTexture->Release(); m_pStagingTexture = nullptr; }
    if (m_pDeskDupl) { m_pDeskDupl->Release(); m_pDeskDupl = nullptr; }
    if (m_pContext) { m_pContext->Release(); m_pContext = nullptr; }
    if (m_pDevice) { m_pDevice->Release(); m_pDevice = nullptr; }

    m_initialized = false;
    std::cout << "ScreenCapture resources released" << std::endl;
}

bool ScreenCapture::InitializeDXGI() {
    HRESULT hr = S_OK;

    D3D_FEATURE_LEVEL featureLevel;
    hr = D3D11CreateDevice(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        0,
        nullptr,
        0,
        D3D11_SDK_VERSION,
        &m_pDevice,
        &featureLevel,
        &m_pContext
    );

    if (FAILED(hr)) {
        std::cerr << "Failed to create D3D11 device" << std::endl;
        return false;
    }

    IDXGIDevice* pDxgiDevice = nullptr;
    hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
    if (FAILED(hr)) {
        std::cerr << "Failed to get DXGI device" << std::endl;
        return false;
    }

    IDXGIAdapter* pDxgiAdapter = nullptr;
    hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
    if (pDxgiDevice) { pDxgiDevice->Release(); pDxgiDevice = nullptr; }
    if (FAILED(hr)) {
        std::cerr << "Failed to get DXGI adapter" << std::endl;
        return false;
    }

    IDXGIOutput* pDxgiOutput = nullptr;
    hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
    if (pDxgiAdapter) { pDxgiAdapter->Release(); pDxgiAdapter = nullptr; }
    if (FAILED(hr)) {
        std::cerr << "Failed to enumerate outputs" << std::endl;
        return false;
    }

    IDXGIOutput1* pDxgiOutput1 = nullptr;
    hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
    if (pDxgiOutput) { pDxgiOutput->Release(); pDxgiOutput = nullptr; }
    if (FAILED(hr)) {
        std::cerr << "Failed to get IDXGIOutput1" << std::endl;
        return false;
    }

    hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
    if (pDxgiOutput1) { pDxgiOutput1->Release(); pDxgiOutput1 = nullptr; }
    if (FAILED(hr)) {
        std::cerr << "Failed to duplicate output" << std::endl;
        return false;
    }

    return true;
}

void ScreenCapture::PreallocateBuffers() {
    int bufferSize = m_screenWidth * m_screenHeight * 4;
    m_frameBuffer.reserve(bufferSize);
}

void ScreenCapture::StartTimer() const {
    QueryPerformanceCounter(&m_startTime);
}

double ScreenCapture::EndTimer() const {
    LARGE_INTEGER endTime;
    QueryPerformanceCounter(&endTime);
    return static_cast<double>(endTime.QuadPart - m_startTime.QuadPart) * 1000.0 / m_frequency.QuadPart;
}

void ScreenCapture::UpdatePerformanceStats(double captureTime, double preprocessTime) const {
    m_stats.captureTimeMs = captureTime;
    m_stats.preprocessTimeMs = preprocessTime;
    m_stats.totalTimeMs = captureTime + preprocessTime;
    m_stats.frameCount++;

    if (m_stats.frameCount % 100 == 0) {
        // Calculate average FPS
    }
}

HRESULT ScreenCapture::GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height) {
    if (!m_pDeskDupl) {
        return E_FAIL;
    }

    HRESULT hr;
    IDXGIResource* pDesktopResource = nullptr;
    DXGI_OUTDUPL_FRAME_INFO frameInfo;

    hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);

    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        return S_FALSE;
    }
    if (FAILED(hr)) {
        return hr;
    }

    ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
    hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
    if (pDesktopResource) { pDesktopResource->Release(); pDesktopResource = nullptr; }
    if (FAILED(hr)) {
        m_pDeskDupl->ReleaseFrame();
        return hr;
    }

    if (!m_pStagingTexture) {
        if (!CreateStagingTexture()) {
            if (pAcquiredDesktopImage) { pAcquiredDesktopImage->Release(); pAcquiredDesktopImage = nullptr; }
            m_pDeskDupl->ReleaseFrame();
            return E_FAIL;
        }
    }

    m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
    if (pAcquiredDesktopImage) { pAcquiredDesktopImage->Release(); pAcquiredDesktopImage = nullptr; }

    D3D11_MAPPED_SUBRESOURCE mappedResource;
    hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
    if (FAILED(hr)) {
        m_pDeskDupl->ReleaseFrame();
        return hr;
    }

    const UINT rowPitch = mappedResource.RowPitch;
    const UINT framePitch = m_screenWidth * 4;
    frameBuffer.resize(framePitch * m_screenHeight);

    BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
    BYTE* pDest = frameBuffer.data();
    for (int y = 0; y < m_screenHeight; ++y) {
        memcpy(pDest, pSource, framePitch);
        pSource += rowPitch;
        pDest += framePitch;
    }

    m_pContext->Unmap(m_pStagingTexture, 0);
    m_pDeskDupl->ReleaseFrame();

    width = m_screenWidth;
    height = m_screenHeight;

    return S_OK;
}

bool ScreenCapture::CreateStagingTexture() {
    DXGI_OUTDUPL_DESC duplDesc;
    m_pDeskDupl->GetDesc(&duplDesc);

    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = duplDesc.ModeDesc.Width;
    desc.Height = duplDesc.ModeDesc.Height;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = duplDesc.ModeDesc.Format;
    desc.SampleDesc.Count = 1;
    desc.SampleDesc.Quality = 0;
    desc.Usage = D3D11_USAGE_STAGING;
    desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    desc.BindFlags = 0;
    desc.MiscFlags = 0;

    HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
    if (FAILED(hr)) {
        std::cerr << "Failed to create staging texture" << std::endl;
        return false;
    }

    return true;
}

std::unique_ptr<ScreenCapture> ScreenCaptureFactory::CreateScreenCapture(
    int roiX, int roiY, int roiWidth, int roiHeight, int targetSize) {

    auto capture = std::make_unique<ScreenCapture>(roiX, roiY, roiWidth, roiHeight, targetSize);

    if (!capture->Initialize()) {
        std::cerr << "Failed to initialize ScreenCapture" << std::endl;
        return nullptr;
    }

    return capture;
}

bool ScreenCaptureFactory::IsDXGISupported() {
    ID3D11Device* pDevice = nullptr;
    ID3D11DeviceContext* pContext = nullptr;

    HRESULT hr = D3D11CreateDevice(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        0,
        nullptr,
        0,
        D3D11_SDK_VERSION,
        &pDevice,
        nullptr,
        &pContext
    );

    bool supported = SUCCEEDED(hr);

    if (pContext) {
        pContext->Release();
        pContext = nullptr;
    }
    if (pDevice) {
        pDevice->Release();
        pDevice = nullptr;
    }

    return supported;
}
