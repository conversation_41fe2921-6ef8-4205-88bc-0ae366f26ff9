#include "../include/HighPerformanceCapture.h"
#include <iostream>
#include <algorithm>

HighPerformanceCapture::HighPerformanceCapture(const Config& config)
    : m_pDevice(nullptr)
    , m_pContext(nullptr)
    , m_pDeskDupl(nullptr)
    , m_pStagingTexture(nullptr)
    , m_pProcessingTexture(nullptr)
    , m_pSRV(nullptr)
    , m_pRTV(nullptr)
    , m_config(config)
    , m_screenWidth(0)
    , m_screenHeight(0)
    , m_initialized(false)
    , m_running(false)
{
    m_bufferPool = std::make_unique<FrameBufferPool>(config.bufferPoolSize);
}

HighPerformanceCapture::~HighPerformanceCapture() {
    Release();
}

bool HighPerformanceCapture::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 获取屏幕尺寸
    m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
    m_screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // 设置默认ROI
    if (m_config.roiX < 0 || m_config.roiY < 0) {
        m_config.roiX = (m_screenWidth - m_config.roiWidth) / 2;
        m_config.roiY = (m_screenHeight - m_config.roiHeight) / 2;
    }

    // 初始化DXGI
    if (!InitializeDXGI()) {
        std::cerr << "Failed to initialize DXGI" << std::endl;
        return false;
    }

    // 初始化GPU处理（如果启用）
    if (m_config.enableGPUProcessing) {
        if (!InitializeGPUProcessing()) {
            std::cerr << "GPU processing not available, falling back to CPU" << std::endl;
            m_config.enableGPUProcessing = false;
        }
    }

    m_initialized = true;
    std::cout << "HighPerformanceCapture initialized successfully" << std::endl;
    std::cout << "Screen: " << m_screenWidth << "x" << m_screenHeight << std::endl;
    std::cout << "ROI: (" << m_config.roiX << ", " << m_config.roiY << ", " 
              << m_config.roiWidth << ", " << m_config.roiHeight << ")" << std::endl;
    std::cout << "Target: " << m_config.targetWidth << "x" << m_config.targetHeight << std::endl;
    std::cout << "GPU Processing: " << (m_config.enableGPUProcessing ? "Enabled" : "Disabled") << std::endl;

    return true;
}

void HighPerformanceCapture::Release() {
    StopAsync();
    
    SAFE_RELEASE(m_pRTV);
    SAFE_RELEASE(m_pSRV);
    SAFE_RELEASE(m_pProcessingTexture);
    SAFE_RELEASE(m_pStagingTexture);
    SAFE_RELEASE(m_pDeskDupl);
    SAFE_RELEASE(m_pContext);
    SAFE_RELEASE(m_pDevice);
    
    m_initialized = false;
    std::cout << "HighPerformanceCapture resources released" << std::endl;
}

cv::Mat HighPerformanceCapture::CaptureFrame() {
    if (!m_initialized) {
        return cv::Mat();
    }

    auto startTime = std::chrono::high_resolution_clock::now();

    // 获取缓冲区
    FrameBuffer* rawBuffer = m_bufferPool->AcquireBuffer();
    FrameBuffer* processedBuffer = m_bufferPool->AcquireBuffer();

    if (!rawBuffer || !processedBuffer) {
        if (rawBuffer) m_bufferPool->ReleaseBuffer(rawBuffer);
        if (processedBuffer) m_bufferPool->ReleaseBuffer(processedBuffer);
        return cv::Mat();
    }

    // 截图
    bool captureSuccess = CaptureRawFrame(rawBuffer);
    auto captureTime = std::chrono::high_resolution_clock::now();
    
    if (!captureSuccess) {
        m_bufferPool->ReleaseBuffer(rawBuffer);
        m_bufferPool->ReleaseBuffer(processedBuffer);
        return cv::Mat();
    }

    // 处理
    bool processSuccess = ProcessFrame(rawBuffer, processedBuffer);
    auto processTime = std::chrono::high_resolution_clock::now();

    // 更新性能统计
    double captureDuration = std::chrono::duration<double, std::milli>(captureTime - startTime).count();
    double processDuration = std::chrono::duration<double, std::milli>(processTime - captureTime).count();
    UpdateCaptureStats(captureDuration);
    UpdateProcessStats(processDuration);

    cv::Mat result;
    if (processSuccess) {
        // 创建OpenCV Mat（不拷贝数据）
        result = cv::Mat(processedBuffer->height, processedBuffer->width, 
                        processedBuffer->channels == 3 ? CV_8UC3 : CV_8UC4, 
                        processedBuffer->GetData()).clone(); // clone确保数据安全
    }

    // 释放缓冲区
    m_bufferPool->ReleaseBuffer(rawBuffer);
    m_bufferPool->ReleaseBuffer(processedBuffer);

    return result;
}

bool HighPerformanceCapture::StartAsync() {
    if (m_running || !m_initialized) {
        return false;
    }

    m_running = true;
    
    // 启动捕获线程
    m_captureThread = std::make_unique<std::thread>(&HighPerformanceCapture::CaptureThreadFunc, this);
    
    // 启动处理线程
    m_processThread = std::make_unique<std::thread>(&HighPerformanceCapture::ProcessThreadFunc, this);
    
    std::cout << "Async capture started" << std::endl;
    return true;
}

void HighPerformanceCapture::StopAsync() {
    if (!m_running) {
        return;
    }

    m_running = false;
    
    // 通知线程退出
    m_captureCV.notify_all();
    m_processedCV.notify_all();
    
    // 等待线程结束
    if (m_captureThread && m_captureThread->joinable()) {
        m_captureThread->join();
    }
    if (m_processThread && m_processThread->joinable()) {
        m_processThread->join();
    }
    
    // 清理队列
    {
        std::lock_guard<std::mutex> lock(m_captureMutex);
        while (!m_captureQueue.empty()) {
            m_bufferPool->ReleaseBuffer(m_captureQueue.front());
            m_captureQueue.pop();
        }
    }
    
    {
        std::lock_guard<std::mutex> lock(m_processedMutex);
        while (!m_processedQueue.empty()) {
            m_bufferPool->ReleaseBuffer(m_processedQueue.front());
            m_processedQueue.pop();
        }
    }
    
    std::cout << "Async capture stopped" << std::endl;
}

cv::Mat HighPerformanceCapture::GetProcessedFrame(int timeoutMs) {
    std::unique_lock<std::mutex> lock(m_processedMutex);
    
    // 等待处理完成的帧
    if (m_processedQueue.empty()) {
        if (timeoutMs > 0) {
            m_processedCV.wait_for(lock, std::chrono::milliseconds(timeoutMs), 
                                 [this] { return !m_processedQueue.empty() || !m_running; });
        }
        
        if (m_processedQueue.empty()) {
            return cv::Mat();
        }
    }
    
    // 获取处理完成的帧
    FrameBuffer* buffer = m_processedQueue.front();
    m_processedQueue.pop();
    lock.unlock();
    
    // 创建OpenCV Mat
    cv::Mat result(buffer->height, buffer->width, 
                  buffer->channels == 3 ? CV_8UC3 : CV_8UC4, 
                  buffer->GetData());
    cv::Mat clonedResult = result.clone();
    
    // 释放缓冲区
    m_bufferPool->ReleaseBuffer(buffer);
    
    return clonedResult;
}

bool HighPerformanceCapture::InitializeDXGI() {
    HRESULT hr = D3D11CreateDevice(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
        nullptr, 0, D3D11_SDK_VERSION,
        &m_pDevice, nullptr, &m_pContext
    );
    
    if (FAILED(hr)) {
        std::cerr << "Failed to create D3D11 device: 0x" << std::hex << hr << std::endl;
        return false;
    }
    
    // 获取DXGI链
    IDXGIDevice* pDxgiDevice = nullptr;
    hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
    if (FAILED(hr)) return false;
    
    IDXGIAdapter* pDxgiAdapter = nullptr;
    hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
    SAFE_RELEASE(pDxgiDevice);
    if (FAILED(hr)) return false;
    
    IDXGIOutput* pDxgiOutput = nullptr;
    hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
    SAFE_RELEASE(pDxgiAdapter);
    if (FAILED(hr)) return false;
    
    IDXGIOutput1* pDxgiOutput1 = nullptr;
    hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
    SAFE_RELEASE(pDxgiOutput);
    if (FAILED(hr)) return false;
    
    hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
    SAFE_RELEASE(pDxgiOutput1);
    if (FAILED(hr)) return false;
    
    return true;
}

bool HighPerformanceCapture::InitializeGPUProcessing() {
    // GPU处理暂时留空，后续可以添加计算着色器进行图像处理
    return false; // 暂时禁用GPU处理
}

bool HighPerformanceCapture::CreateStagingTexture() {
    if (m_pStagingTexture) {
        return true;
    }
    
    DXGI_OUTDUPL_DESC duplDesc;
    m_pDeskDupl->GetDesc(&duplDesc);
    
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = duplDesc.ModeDesc.Width;
    desc.Height = duplDesc.ModeDesc.Height;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = duplDesc.ModeDesc.Format;
    desc.SampleDesc.Count = 1;
    desc.SampleDesc.Quality = 0;
    desc.Usage = D3D11_USAGE_STAGING;
    desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    desc.BindFlags = 0;
    desc.MiscFlags = 0;
    
    HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
    return SUCCEEDED(hr);
}

bool HighPerformanceCapture::CaptureRawFrame(FrameBuffer* buffer) {
    if (!m_pDeskDupl || !buffer) {
        return false;
    }

    HRESULT hr;
    IDXGIResource* pDesktopResource = nullptr;
    DXGI_OUTDUPL_FRAME_INFO frameInfo;

    // 获取下一帧（不等待）
    hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);

    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        return false; // 没有新帧
    }
    if (FAILED(hr)) {
        return false;
    }

    // 转换为纹理
    ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
    hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
    SAFE_RELEASE(pDesktopResource);
    if (FAILED(hr)) {
        m_pDeskDupl->ReleaseFrame();
        return false;
    }

    // 创建中转纹理
    if (!CreateStagingTexture()) {
        SAFE_RELEASE(pAcquiredDesktopImage);
        m_pDeskDupl->ReleaseFrame();
        return false;
    }

    // 复制到中转纹理
    m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
    SAFE_RELEASE(pAcquiredDesktopImage);

    // 映射纹理数据
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
    if (FAILED(hr)) {
        m_pDeskDupl->ReleaseFrame();
        return false;
    }

    // 准备缓冲区
    buffer->Resize(m_screenWidth, m_screenHeight, 4); // BGRA
    buffer->timestamp = std::chrono::high_resolution_clock::now();

    // 高效内存拷贝
    const UINT rowPitch = mappedResource.RowPitch;
    const UINT framePitch = m_screenWidth * 4;

    BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
    BYTE* pDest = buffer->GetData();

    if (rowPitch == framePitch) {
        // 一次性拷贝
        memcpy(pDest, pSource, framePitch * m_screenHeight);
    } else {
        // 逐行拷贝
        for (int y = 0; y < m_screenHeight; ++y) {
            memcpy(pDest, pSource, framePitch);
            pSource += rowPitch;
            pDest += framePitch;
        }
    }

    // 清理
    m_pContext->Unmap(m_pStagingTexture, 0);
    m_pDeskDupl->ReleaseFrame();

    return true;
}

bool HighPerformanceCapture::ProcessFrame(FrameBuffer* input, FrameBuffer* output) {
    if (!input || !output) {
        return false;
    }

    if (m_config.enableGPUProcessing) {
        return ProcessFrameGPU(input, output);
    } else {
        return ProcessFrameCPU(input, output);
    }
}

bool HighPerformanceCapture::ProcessFrameGPU(FrameBuffer* input, FrameBuffer* output) {
    // GPU处理暂未实现
    return ProcessFrameCPU(input, output);
}

bool HighPerformanceCapture::ProcessFrameCPU(FrameBuffer* input, FrameBuffer* output) {
    if (!input || !output) {
        return false;
    }

    // 1. 提取ROI
    cv::Mat inputMat(input->height, input->width, CV_8UC4, input->GetData());
    cv::Rect roi(m_config.roiX, m_config.roiY, m_config.roiWidth, m_config.roiHeight);

    // 确保ROI在图像范围内
    roi.x = std::max(0, std::min(roi.x, input->width - 1));
    roi.y = std::max(0, std::min(roi.y, input->height - 1));
    roi.width = std::min(roi.width, input->width - roi.x);
    roi.height = std::min(roi.height, input->height - roi.y);

    cv::Mat roiMat = inputMat(roi);

    // 2. BGRA → RGB转换
    cv::Mat rgbMat;
    cv::cvtColor(roiMat, rgbMat, cv::COLOR_BGRA2RGB);

    // 3. 尺寸调整
    cv::Mat resizedMat;
    cv::resize(rgbMat, resizedMat, cv::Size(m_config.targetWidth, m_config.targetHeight), 0, 0, cv::INTER_LINEAR);

    // 4. 复制到输出缓冲区
    output->Resize(m_config.targetWidth, m_config.targetHeight, 3);
    output->timestamp = input->timestamp;
    memcpy(output->GetData(), resizedMat.data, output->GetSize());

    return true;
}

void HighPerformanceCapture::CaptureThreadFunc() {
    std::cout << "Capture thread started" << std::endl;

    while (m_running) {
        // 获取缓冲区
        FrameBuffer* buffer = m_bufferPool->AcquireBuffer();
        if (!buffer) {
            std::this_thread::sleep_for(std::chrono::microseconds(100));
            continue;
        }

        // 截图
        auto startTime = std::chrono::high_resolution_clock::now();
        bool success = CaptureRawFrame(buffer);
        auto endTime = std::chrono::high_resolution_clock::now();

        if (success) {
            double captureTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
            UpdateCaptureStats(captureTime);

            // 添加到处理队列
            {
                std::lock_guard<std::mutex> lock(m_captureMutex);
                if (m_captureQueue.size() < m_config.maxQueueSize) {
                    m_captureQueue.push(buffer);
                    m_captureCV.notify_one();
                } else {
                    // 队列满了，丢弃最老的帧
                    FrameBuffer* oldBuffer = m_captureQueue.front();
                    m_captureQueue.pop();
                    m_bufferPool->ReleaseBuffer(oldBuffer);
                    m_captureQueue.push(buffer);
                    m_captureCV.notify_one();
                }
            }
        } else {
            m_bufferPool->ReleaseBuffer(buffer);
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    }

    std::cout << "Capture thread stopped" << std::endl;
}

void HighPerformanceCapture::ProcessThreadFunc() {
    std::cout << "Process thread started" << std::endl;

    while (m_running) {
        FrameBuffer* inputBuffer = nullptr;

        // 等待捕获的帧
        {
            std::unique_lock<std::mutex> lock(m_captureMutex);
            m_captureCV.wait(lock, [this] { return !m_captureQueue.empty() || !m_running; });

            if (!m_running) break;

            if (!m_captureQueue.empty()) {
                inputBuffer = m_captureQueue.front();
                m_captureQueue.pop();
            }
        }

        if (!inputBuffer) continue;

        // 获取输出缓冲区
        FrameBuffer* outputBuffer = m_bufferPool->AcquireBuffer();
        if (!outputBuffer) {
            m_bufferPool->ReleaseBuffer(inputBuffer);
            continue;
        }

        // 处理帧
        auto startTime = std::chrono::high_resolution_clock::now();
        bool success = ProcessFrame(inputBuffer, outputBuffer);
        auto endTime = std::chrono::high_resolution_clock::now();

        if (success) {
            double processTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();
            UpdateProcessStats(processTime);

            // 添加到输出队列
            {
                std::lock_guard<std::mutex> lock(m_processedMutex);
                if (m_processedQueue.size() < m_config.maxQueueSize) {
                    m_processedQueue.push(outputBuffer);
                    m_processedCV.notify_one();
                } else {
                    // 队列满了，丢弃最老的帧
                    FrameBuffer* oldBuffer = m_processedQueue.front();
                    m_processedQueue.pop();
                    m_bufferPool->ReleaseBuffer(oldBuffer);
                    m_processedQueue.push(outputBuffer);
                    m_processedCV.notify_one();
                }
            }
        } else {
            m_bufferPool->ReleaseBuffer(outputBuffer);
        }

        // 释放输入缓冲区
        m_bufferPool->ReleaseBuffer(inputBuffer);
    }

    std::cout << "Process thread stopped" << std::endl;
}

void HighPerformanceCapture::UpdateCaptureStats(double captureTime) {
    m_perfCounter.IncrementCapture(captureTime);
}

void HighPerformanceCapture::UpdateProcessStats(double processTime) {
    m_perfCounter.IncrementProcess(processTime);
}

void HighPerformanceCapture::UpdateConfig(const Config& config) {
    m_config = config;
    // 注意：某些配置更改可能需要重新初始化
}

// HighPerformanceCaptureFactory 实现
std::unique_ptr<HighPerformanceCapture> HighPerformanceCaptureFactory::CreateCapture(
    const HighPerformanceCapture::Config& config) {

    auto capture = std::make_unique<HighPerformanceCapture>(config);

    if (!capture->Initialize()) {
        std::cerr << "Failed to initialize HighPerformanceCapture" << std::endl;
        return nullptr;
    }

    return capture;
}

bool HighPerformanceCaptureFactory::TestSystemCapabilities() {
    // 测试系统是否支持DXGI
    ID3D11Device* pDevice = nullptr;
    ID3D11DeviceContext* pContext = nullptr;

    HRESULT hr = D3D11CreateDevice(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
        nullptr, 0, D3D11_SDK_VERSION,
        &pDevice, nullptr, &pContext
    );

    bool supported = SUCCEEDED(hr);

    SAFE_RELEASE(pContext);
    SAFE_RELEASE(pDevice);

    return supported;
}

HighPerformanceCapture::Config HighPerformanceCaptureFactory::GetOptimalConfig() {
    HighPerformanceCapture::Config config;

    // 根据系统性能调整配置
    config.enableAsync = true;
    config.enableGPUProcessing = false; // 暂时禁用
    config.bufferPoolSize = 15; // 增加缓冲区池大小
    config.maxQueueSize = 3;    // 减少队列大小以降低延迟

    return config;
}
