import os
import sys
from PIL import ImageFont, Image, ImageDraw
import numpy as np

def test_font(font_path):
    """测试字体是否支持中文"""
    try:
        # 创建一个简单的图像
        img = Image.new('RGB', (200, 100), color=(255, 255, 255))
        d = ImageDraw.Draw(img)
        
        # 尝试使用此字体渲染中文
        font = ImageFont.truetype(font_path, 20)
        d.text((10, 10), "测试中文字体", font=font, fill=(0, 0, 0))
        
        # 检查是否有字符被渲染
        img_array = np.array(img)
        if np.sum(255 - img_array) > 100:  # 如果有一些像素变黑了
            return True
        return False
    except Exception as e:
        print(f"测试字体 {font_path} 时出错: {e}")
        return False

def find_chinese_fonts():
    """查找系统中支持中文的字体"""
    print("正在查找支持中文的字体...")
    
    # 常见的字体目录
    font_dirs = []
    
    # Windows字体目录
    if os.name == 'nt':
        font_dirs.append(os.path.join(os.environ['WINDIR'], 'Fonts'))
        
    # Linux字体目录
    elif os.name == 'posix':
        font_dirs.extend([
            '/usr/share/fonts',
            '/usr/local/share/fonts',
            os.path.expanduser('~/.fonts')
        ])
    
    # Mac字体目录
    elif sys.platform == 'darwin':
        font_dirs.extend([
            '/Library/Fonts',
            '/System/Library/Fonts',
            os.path.expanduser('~/Library/Fonts')
        ])
    
    # 记录找到的支持中文的字体
    chinese_fonts = []
    
    # 遍历字体目录
    for font_dir in font_dirs:
        if not os.path.exists(font_dir):
            continue
            
        print(f"正在搜索目录: {font_dir}")
        
        for root, _, files in os.walk(font_dir):
            for file in files:
                if file.lower().endswith(('.ttf', '.ttc', '.otf')):
                    font_path = os.path.join(root, file)
                    
                    # 测试字体是否支持中文
                    if test_font(font_path):
                        chinese_fonts.append(font_path)
                        print(f"找到中文字体: {file} ({font_path})")
    
    print(f"\n找到 {len(chinese_fonts)} 个支持中文的字体:")
    for i, font in enumerate(chinese_fonts, 1):
        print(f"{i}. {os.path.basename(font)}: {font}")
        
    return chinese_fonts

if __name__ == "__main__":
    chinese_fonts = find_chinese_fonts()
    
    if chinese_fonts:
        print("\n您可以使用以下字体路径:")
        print('font_path="' + chinese_fonts[0] + '"')
    else:
        print("\n未找到支持中文的字体。")
    
    print("\n按Enter键退出...")
    input() 