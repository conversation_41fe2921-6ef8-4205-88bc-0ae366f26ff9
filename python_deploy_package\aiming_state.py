import numpy as np
import threading
from typing import Tuple

class AimingState:
    """线程安全的状态管理器，用于在视觉线程和控制线程之间传递数据。"""
    def __init__(self):
        self._lock = threading.Lock()
        self.running = True
        self.aiming_enabled = False
        
        # --- 核心状态变量 ---
        self.target_xy = None         # 目标在截图区域的坐标 (x, y)
        self.target_velocity = np.array([0.0, 0.0]) # 初始化速度为零向量
        self.new_target_set = False   # 一个标志，用于通知控制线程有新目标

    def update_aim_status(self, is_aiming: bool):
        """由视觉线程调用，仅更新瞄准按键状态。"""
        with self._lock:
            self.aiming_enabled = is_aiming
            # 如果停止瞄准，也应该清除目标信息
            if not is_aiming:
                self.target_xy = None
                self.target_velocity = np.array([0.0, 0.0])
                self.new_target_set = False

    def update_target(self, xy: np.ndarray, velocity: np.ndarray):
        """视觉线程调用此方法来更新最新的目标信息。"""
        with self._lock:
            self.target_xy = xy
            self.target_velocity = velocity
            self.new_target_set = True

    def clear_target(self):
        """当目标丢失时（但在瞄准键仍按下时），由视觉线程调用。"""
        with self._lock:
            self.target_xy = None
            self.target_velocity = np.array([0.0, 0.0])
            # new_target_set 保持不变，让控制线程知道有状态变化

    def get_control_info(self) -> Tuple[bool, np.ndarray, np.ndarray, bool]:
        """
        控制线程调用此方法来获取执行计算所需的所有信息。
        返回: (是否启用自瞄, 目标位置, 目标速度, 是否是新目标)
        """
        with self._lock:
            pos = self.target_xy if self.target_xy is not None else np.array([0.0, 0.0])
            vel = self.target_velocity

            new_target = self.new_target_set
            # 读取后立即重置新目标标志
            if self.new_target_set:
                self.new_target_set = False

            return self.aiming_enabled, pos, vel, new_target

    def has_valid_target(self) -> bool:
        """
        检查是否有有效的目标
        返回: True如果有有效目标，False如果没有
        """
        with self._lock:
            return self.target_xy is not None

    def request_stop(self):
        """请求所有使用此状态的线程停止。"""
        with self._lock:
            self.running = False

    def is_running(self) -> bool:
        """检查是否应该继续运行。"""
        with self._lock:
            return self.running 
 