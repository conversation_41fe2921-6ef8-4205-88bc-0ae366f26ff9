#pragma once

#include "Common.h"

/**
 * PID控制器类
 * 用于精确的鼠标移动控制
 */
class PIDController {
public:
    /**
     * 构造函数
     * @param params PID参数
     */
    PIDController(const PIDParams& params);
    
    /**
     * 析构函数
     */
    ~PIDController() = default;

    /**
     * 计算控制输出
     * @param error 误差向量 (目标位置 - 当前位置)
     * @param deltaTime 时间间隔(秒)
     * @return 控制输出 (dx, dy)
     */
    cv::Point2f Compute(const cv::Point2f& error, double deltaTime);

    /**
     * 重置控制器状态
     */
    void Reset();

    /**
     * 更新PID参数
     * @param params 新的PID参数
     */
    void UpdateParams(const PIDParams& params);

    /**
     * 获取当前PID参数
     */
    PIDParams GetParams() const { return m_params; }

    /**
     * 获取当前积分系数 (自适应)
     * @param error 当前误差
     * @return 当前积分系数
     */
    float GetCurrentKi(const cv::Point2f& error) const;

    /**
     * 获取控制器状态信息
     */
    struct ControllerState {
        cv::Point2f lastError;
        cv::Point2f integral;
        cv::Point2f derivative;
        cv::Point2f output;
        float currentKi;
        double lastTime;
    };
    
    ControllerState GetState() const;

private:
    // PID参数
    PIDParams m_params;
    
    // 控制器状态
    cv::Point2f m_lastError;
    cv::Point2f m_integral;
    double m_lastTime;
    bool m_firstRun;
    
    // 私有方法
    float CalculateAdaptiveKi(const cv::Point2f& error) const;
    cv::Point2f ClampOutput(const cv::Point2f& output) const;
};

/**
 * 高级PID控制器
 * 支持更多高级功能
 */
class AdvancedPIDController : public PIDController {
public:
    /**
     * 构造函数
     * @param params PID参数
     */
    AdvancedPIDController(const PIDParams& params);
    
    /**
     * 析构函数
     */
    ~AdvancedPIDController() = default;

    /**
     * 计算控制输出 (重载版本，支持更多功能)
     * @param error 误差向量
     * @param deltaTime 时间间隔
     * @param targetVelocity 目标速度 (可选)
     * @return 控制输出
     */
    cv::Point2f ComputeAdvanced(
        const cv::Point2f& error,
        double deltaTime,
        const cv::Point2f& targetVelocity = cv::Point2f(0, 0)
    );

    /**
     * 设置输出限制
     * @param minOutput 最小输出
     * @param maxOutput 最大输出
     */
    void SetOutputLimits(const cv::Point2f& minOutput, const cv::Point2f& maxOutput);

    /**
     * 设置积分限制 (防止积分饱和)
     * @param maxIntegral 最大积分值
     */
    void SetIntegralLimits(float maxIntegral);

    /**
     * 启用/禁用微分项滤波
     * @param enable 是否启用
     * @param filterCoeff 滤波系数 (0-1)
     */
    void SetDerivativeFilter(bool enable, float filterCoeff = 0.1f);

private:
    // 输出限制
    cv::Point2f m_minOutput;
    cv::Point2f m_maxOutput;
    bool m_hasOutputLimits;
    
    // 积分限制
    float m_maxIntegral;
    bool m_hasIntegralLimits;
    
    // 微分滤波
    bool m_useDerivativeFilter;
    float m_derivativeFilterCoeff;
    cv::Point2f m_filteredDerivative;
    
    // 私有方法
    cv::Point2f ApplyDerivativeFilter(const cv::Point2f& derivative);
    cv::Point2f ClampIntegral(const cv::Point2f& integral) const;
};
