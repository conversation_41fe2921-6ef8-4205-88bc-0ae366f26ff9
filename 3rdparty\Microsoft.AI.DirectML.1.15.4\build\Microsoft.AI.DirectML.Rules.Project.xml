<?xml version="1.0" encoding="utf-8"?>
<Rule
      Name="Microsoft_AI_DirectML"
      DisplayName="DirectML NuGet"
      Order="70"
      PageTemplate="generic"
      Description="Options for the Microsoft.AI.DirectML NuGet Package"
      xmlns="http://schemas.microsoft.com/build/2009/properties">

  <Rule.Categories>
    <Category Name="General" DisplayName="General" />
  </Rule.Categories>

  <Rule.DataSource>
    <DataSource Persistence="ProjectFile" Label="Globals" />
  </Rule.DataSource>

  <BoolProperty Name="Microsoft_AI_DirectML_SkipLibraryCopy"
                DisplayName="Disable Library Copy"
                Description="Disables copying DirectML.dll and DirectML.pdb into the project output directory."
                Category="General" />

  <BoolProperty Name="Microsoft_AI_DirectML_SkipDebugLayerCopy"
                DisplayName="Disable Debug Layer Copy"
                Description="Disables copying DirectML.Debug.dll and DirectML.Debug.pdb into the project output directory. This 'debug layer' is used for development only."
                Category="General" />

  <BoolProperty Name="Microsoft_AI_DirectML_SkipLink"
                DisplayName="Disable Linking"
                Description="Disables linking with the package's import library appending the package's lib folder as an additional library directory."
                Category="General" />

  <BoolProperty Name="Microsoft_AI_DirectML_SkipIncludeDir"
                DisplayName="Disable Include Directory"
                Description="Disables appending the package's include folder as an additional include directory."
                Category="General" />

</Rule>
