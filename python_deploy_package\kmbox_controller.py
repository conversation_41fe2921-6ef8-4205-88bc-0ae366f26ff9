import time
import numpy as np
import os
import sys

# 自动添加bin目录到sys.path和DLL搜索路径
bin_path = os.path.join(os.path.dirname(__file__), 'bin')
if bin_path not in sys.path:
    sys.path.append(bin_path)
if hasattr(os, 'add_dll_directory'):
    os.add_dll_directory(bin_path)

# 尝试导入kmNet模块
try:
    import kmNet
    KMBOX_AVAILABLE = True
    print("成功加载kmBox Net模块!")
except ImportError:
    KMBOX_AVAILABLE = False
    print("未找到kmBox Net模块，请确保已正确安装并配置!")

class KMBoxController:
    """KMBox硬件控制器类，用于通过kmboxNet硬件控制鼠标移动"""
    
    def __init__(self, ip="************", port="6234", mac="12345"):
        """
        初始化KMBox控制器
        
        参数:
            ip (str): 盒子的IP地址
            port (str): 通信端口号
            mac (str): 盒子的MAC地址
        """
        self.available = KMBOX_AVAILABLE
        self.initialized = False
        self.debug = True  # 开启调试输出
        
        # 如果模块可用，尝试初始化
        if self.available:
            try:
                result = kmNet.init(ip, port, mac)
                if result == 0:
                    self.initialized = True
                    print(f"KMBox硬件初始化成功! IP: {ip}, Port: {port}")
                else:
                    print(f"KMBox硬件初始化失败，错误码: {result}")
            except Exception as e:
                print(f"KMBox硬件初始化出错: {e}")
    
    def is_ready(self):
        """检查KMBox是否准备就绪"""
        return self.available and self.initialized
    
    def move_mouse(self, dx, dy, mode="normal"):
        """
        移动鼠标
        
        参数:
            dx (int): X方向移动的像素数
            dy (int): Y方向移动的像素数
            mode (str): 移动模式，可选值:
                - "normal": 直接移动，无轨迹模拟
                - "human": 模拟人类移动
                - "beizer": 贝塞尔曲线移动
        
        返回:
            bool: 是否成功移动
        """
        if not self.is_ready():
            return False
            
        # 确保参数为整数并打印调试信息
        dx = int(dx)
        dy = int(dy)
        
        # 如果移动量太小，增大到可感知的程度
        if abs(dx) < 2 and abs(dy) < 2 and (dx != 0 or dy != 0):
            if abs(dx) > 0:
                dx = 2 if dx > 0 else -2
            if abs(dy) > 0:
                dy = 2 if dy > 0 else -2
        
        if self.debug and (dx != 0 or dy != 0):
            print(f"\nKMBox移动: dx={dx}, dy={dy}, mode={mode}")
            
        try:
            if dx == 0 and dy == 0:
                return True  # 如果没有移动，直接返回
            
            if mode == "normal":
                # 直接移动，无轨迹模拟
                if self.debug:
                    print(f"KMBox使用normal模式移动: ({dx}, {dy})")
                return_value = kmNet.move(dx, dy)
                if self.debug:
                    print(f"KMBox移动结果: {return_value}")
                
            elif mode == "human":
                # 模拟人类移动，计算合适的时间
                distance = np.sqrt(dx**2 + dy**2)
                # 根据距离计算合理的移动时间 (50-300ms)
                move_time = int(min(max(distance * 2, 50), 300))
                if self.debug:
                    print(f"KMBox使用human模式移动: ({dx}, {dy}), 时间: {move_time}ms")
                return_value = kmNet.move_auto(dx, dy, move_time)
                if self.debug:
                    print(f"KMBox移动结果: {return_value}")
                
            elif mode == "beizer":
                # 贝塞尔曲线移动
                distance = np.sqrt(dx**2 + dy**2)
                move_time = int(min(max(distance * 2, 50), 300))
                
                # 生成合理的控制点
                # 控制点偏移量为移动距离的1/3到1/2
                offset_factor = np.random.uniform(0.3, 0.5)
                
                # 计算控制点，添加一些随机性
                control_x1 = int(dx * offset_factor * np.random.uniform(0.8, 1.2))
                control_y1 = int(dy * offset_factor * np.random.uniform(0.8, 1.2))
                
                control_x2 = int(dx * offset_factor * np.random.uniform(0.8, 1.2))
                control_y2 = int(dy * offset_factor * np.random.uniform(0.8, 1.2))
                
                if self.debug:
                    print(f"KMBox使用beizer模式移动: ({dx}, {dy}), 时间: {move_time}ms")
                    print(f"控制点: ({control_x1}, {control_y1}), ({control_x2}, {control_y2})")
                
                return_value = kmNet.move_beizer(dx, dy, move_time, 
                                 control_x1, control_y1, 
                                 control_x2, control_y2)
                if self.debug:
                    print(f"KMBox移动结果: {return_value}")
                
            # 小延时确保命令被执行
            time.sleep(0.01)
            return True
        except Exception as e:
            print(f"KMBox移动鼠标出错: {e}")
            return False
            
    def click(self, button="left", state=1):
        """
        控制鼠标点击
        
        参数:
            button (str): 按钮，可选值: "left", "right", "middle"
            state (int): 状态，1表示按下，0表示松开
        
        返回:
            bool: 是否成功执行
        """
        if not self.is_ready():
            return False
            
        try:
            if button == "left":
                kmNet.left(state)
            elif button == "right":
                kmNet.right(state)
            elif button == "middle":
                kmNet.middle(state)
            return True
        except Exception as e:
            print(f"KMBox控制鼠标按键出错: {e}")
            return False
    
    def close(self):
        """关闭监控功能"""
        if self.is_ready():
            try:
                kmNet.monitor(0)  # 关闭监控功能
            except Exception as e:
                print(f"关闭KMBox监控出错: {e}")

# 创建一个全局控制器实例
kmbox_controller = None

def init_kmbox(ip="************", port="6234", mac="12345"):
    """初始化KMBox控制器"""
    global kmbox_controller
    kmbox_controller = KMBoxController(ip, port, mac)
    return kmbox_controller.is_ready()

def move_mouse_kmbox(dx, dy, mode="normal"):
    """
    使用KMBox移动鼠标
    
    参数:
        dx (int): X方向移动的像素数
        dy (int): Y方向移动的像素数
        mode (str): 移动模式，可选值: "normal", "human", "beizer"
    
    返回:
        bool: 是否成功移动
    """
    if kmbox_controller is None or not kmbox_controller.is_ready():
        print("KMBox控制器未初始化或不可用")
        return False
        
    return kmbox_controller.move_mouse(dx, dy, mode)

def cleanup():
    """清理KMBox资源"""
    if kmbox_controller is not None:
        kmbox_controller.close() 