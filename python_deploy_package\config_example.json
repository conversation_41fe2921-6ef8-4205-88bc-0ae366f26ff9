{"model": {"path": "models/PUBGV8_320.onnx", "model_type": "yolov8", "num_classes": 2, "class_names": ["head", "body"], "confidence_threshold": 0.5, "nms_threshold": 0.4}, "capture": {"method": "screen", "screen_resolution": [1920, 1080], "udp": {"host": "127.0.0.1", "port": 12345, "buffer_size": 65536}}, "mouse": {"move_mode": "system", "aim_keys": {"priority_1": [{"key": 5, "offset_y": 0.13, "trigger": {"enabled": false, "fire_delay_ms": 50, "fire_interval_ms": 120}}], "priority_2": [{"key": 6, "offset_y": 0.5, "trigger": {"enabled": false, "fire_delay_ms": 50, "fire_interval_ms": 120}}]}, "kmbox": {"ip": "*************", "port": 1408, "mac": "AABBCCDDEEFF", "move_type": "move"}}, "pid": {"control_frequency": 200, "system": {"kp": 2.5, "ki": 0.1, "kd": 0.05, "base_ki": 0.0, "max_ki": 0.5, "inner_threshold": 10.0, "outer_threshold": 50.0}, "ghub": {"kp": 3.0, "ki": 0.15, "kd": 0.08, "base_ki": 0.0, "max_ki": 0.6, "inner_threshold": 8.0, "outer_threshold": 40.0}, "kmbox": {"kp": 2.0, "ki": 0.08, "kd": 0.03, "base_ki": 0.0, "max_ki": 0.4, "inner_threshold": 12.0, "outer_threshold": 60.0}}, "display": {"show_preview": true, "font_path": "C:/Windows/Fonts/msyh.ttc", "font_size": 20}, "aim_circle_radius": 180, "debug": {"show_timing": false}}