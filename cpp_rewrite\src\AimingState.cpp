#include "../include/AimingState.h"

AimingState::AimingState()
    : m_running(true)
    , m_debugMode(false)
    , m_isAiming(false)
    , m_newTargetAvailable(false)
    , m_currentTarget(nullptr)
    , m_crosshair(0, 0)
    , m_totalError(0, 0)
    , m_offsetY(0.0f)
    , m_frameCount(0)
{
    m_performanceStats.Reset();
    memset(&m_debugInfo, 0, sizeof(m_debugInfo));
    m_lastUpdateTime = Utils::GetCurrentTime();
}

void AimingState::UpdateAimStatus(bool isAiming) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_isAiming = isAiming;
    
    if (m_debugMode) {
        m_debugInfo.isAiming = isAiming;
    }
}

void AimingState::UpdateTarget(const std::shared_ptr<Detection>& target, const cv::Point2f& crosshair, float offsetY) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_currentTarget = target;
    m_crosshair = crosshair;
    m_offsetY = offsetY;
    m_newTargetAvailable = true;
    
    if (target) {
        // 计算瞄准点 (考虑Y轴偏移)
        cv::Point2f targetPoint = CalculateTargetPoint(*target, offsetY);
        
        // 计算总误差
        m_totalError = targetPoint - crosshair;
        
        if (m_debugMode) {
            m_debugInfo.targetCenter = target->center;
            m_debugInfo.targetConfidence = target->confidence;
            m_debugInfo.targetClass = target->classId;
            m_debugInfo.totalError = m_totalError;
        }
    } else {
        m_totalError = cv::Point2f(0, 0);
    }
    
    UpdateDebugInfo();
}

bool AimingState::GetControlInfo(bool& isAiming, cv::Point2f& totalError, std::shared_ptr<Detection>& targetInfo, bool& newTargetAvailable) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    isAiming = m_isAiming;
    totalError = m_totalError;
    targetInfo = m_currentTarget;
    newTargetAvailable = m_newTargetAvailable;
    
    // 重置新目标标志
    m_newTargetAvailable = false;
    
    return m_isAiming && m_currentTarget != nullptr;
}

void AimingState::RequestStop() {
    m_running = false;
    LOG_INFO("Stop requested");
}

bool AimingState::IsRunning() const {
    return m_running;
}

void AimingState::UpdatePerformanceStats(const PerformanceStats& stats) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_performanceStats = stats;
}

PerformanceStats AimingState::GetPerformanceStats() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_performanceStats;
}

AimingState::DebugInfo AimingState::GetDebugInfo() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_debugInfo;
}

cv::Point2f AimingState::CalculateTargetPoint(const Detection& target, float offsetY) const {
    cv::Point2f targetPoint = target.center;
    
    // 应用Y轴偏移 (向上偏移为负值)
    float boxHeight = target.box.height;
    targetPoint.y -= boxHeight * offsetY;
    
    return targetPoint;
}

void AimingState::UpdateDebugInfo() {
    if (!m_debugMode) return;
    
    m_debugInfo.lastUpdateTime = Utils::GetCurrentTime();
    m_debugInfo.frameCount = ++m_frameCount;
}
