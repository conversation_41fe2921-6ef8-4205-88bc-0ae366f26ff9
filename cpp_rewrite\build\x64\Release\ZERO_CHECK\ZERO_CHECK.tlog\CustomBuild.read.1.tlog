^C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\BUILD\CMAKEFILES\E9C5D4D3290504AC529CAACE258F5B03\GENERATE.STAMP.RULE
C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\CMAKELISTS.TXT
C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\BUILD\CMAKEFILES\3.23.0\CMAKECXXCOMPILER.CMAKE
C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\BUILD\CMAKEFILES\3.23.0\CMAKERCCOMPILER.CMAKE
C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\BUILD\CMAKEFILES\3.23.0\CMAKESYSTEM.CMAKE
C:\USERS\<USER>\DESKTOP\FOYE_111111\CPP_REWRITE\CONFIG\CONFIG.JSON
D:\3RD_PARTY\OPENCV_4.8\OPENCV\BUILD\OPENCVCONFIG-VERSION.CMAKE
D:\3RD_PARTY\OPENCV_4.8\OPENCV\BUILD\OPENCVCONFIG.CMAKE
D:\3RD_PARTY\OPENCV_4.8\OPENCV\BUILD\X64\VC16\LIB\OPENCVCONFIG.CMAKE
D:\3RD_PARTY\OPENCV_4.8\OPENCV\BUILD\X64\VC16\LIB\OPENCVMODULES-DEBUG.CMAKE
D:\3RD_PARTY\OPENCV_4.8\OPENCV\BUILD\X64\VC16\LIB\OPENCVMODULES-RELEASE.CMAKE
D:\3RD_PARTY\OPENCV_4.8\OPENCV\BUILD\X64\VC16\LIB\OPENCVMODULES.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKECXXINFORMATION.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKECOMMONLANGUAGEINCLUDE.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKEGENERICSYSTEM.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKEINITIALIZECONFIGS.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKELANGUAGEINFORMATION.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKERCINFORMATION.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKESYSTEMSPECIFICINFORMATION.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\CMAKESYSTEMSPECIFICINITIALIZE.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\COMPILER\CMAKECOMMONCOMPILERMACROS.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\COMPILER\MSVC-CXX.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\FINDPACKAGEHANDLESTANDARDARGS.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\FINDPACKAGEMESSAGE.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\PLATFORM\WINDOWS-MSVC-CXX.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\PLATFORM\WINDOWS-MSVC.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\PLATFORM\WINDOWS.CMAKE
D:\C++\SHARE\CMAKE-3.23\MODULES\PLATFORM\WINDOWSPATHS.CMAKE
