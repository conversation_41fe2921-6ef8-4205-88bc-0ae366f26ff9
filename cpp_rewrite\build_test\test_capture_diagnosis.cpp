#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <iostream>
#include <chrono>
#include <thread>

#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

class CaptureDebugger {
private:
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    
public:
    CaptureDebugger() : m_pDevice(nullptr), m_pContext(nullptr), m_pDeskDupl(nullptr) {}
    
    ~CaptureDebugger() {
        SAFE_RELEASE(m_pDeskDupl);
        SAFE_RELEASE(m_pContext);
        SAFE_RELEASE(m_pDevice);
    }
    
    bool Initialize() {
        HRESULT hr = D3D11CreateDevice(
            nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
            nullptr, 0, D3D11_SDK_VERSION,
            &m_pDevice, nullptr, &m_pContext
        );
        
        if (FAILED(hr)) {
            std::cerr << "Failed to create D3D11 device" << std::endl;
            return false;
        }
        
        // 获取DXGI链
        IDXGIDevice* pDxgiDevice = nullptr;
        hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
        if (FAILED(hr)) return false;
        
        IDXGIAdapter* pDxgiAdapter = nullptr;
        hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
        SAFE_RELEASE(pDxgiDevice);
        if (FAILED(hr)) return false;
        
        IDXGIOutput* pDxgiOutput = nullptr;
        hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
        SAFE_RELEASE(pDxgiAdapter);
        if (FAILED(hr)) return false;
        
        // 获取显示器信息
        DXGI_OUTPUT_DESC outputDesc;
        pDxgiOutput->GetDesc(&outputDesc);
        std::wcout << L"Monitor: " << outputDesc.DeviceName << std::endl;
        std::cout << "Desktop coordinates: (" << outputDesc.DesktopCoordinates.left 
                  << ", " << outputDesc.DesktopCoordinates.top << ") to ("
                  << outputDesc.DesktopCoordinates.right << ", " << outputDesc.DesktopCoordinates.bottom << ")" << std::endl;
        
        IDXGIOutput1* pDxgiOutput1 = nullptr;
        hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
        SAFE_RELEASE(pDxgiOutput);
        if (FAILED(hr)) return false;
        
        hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
        SAFE_RELEASE(pDxgiOutput1);
        if (FAILED(hr)) {
            std::cerr << "Failed to duplicate output: 0x" << std::hex << hr << std::endl;
            return false;
        }
        
        // 获取复制描述信息
        DXGI_OUTDUPL_DESC duplDesc;
        m_pDeskDupl->GetDesc(&duplDesc);
        std::cout << "Duplication mode: " << duplDesc.ModeDesc.Width << "x" << duplDesc.ModeDesc.Height 
                  << " @ " << duplDesc.ModeDesc.RefreshRate.Numerator << "/" << duplDesc.ModeDesc.RefreshRate.Denominator << "Hz" << std::endl;
        std::cout << "Format: " << duplDesc.ModeDesc.Format << std::endl;
        
        return true;
    }
    
    void TestFrameAcquisition() {
        std::cout << "\n=== Frame Acquisition Test ===" << std::endl;
        
        int successCount = 0;
        int timeoutCount = 0;
        int errorCount = 0;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        auto endTime = startTime + std::chrono::seconds(5);
        
        while (std::chrono::high_resolution_clock::now() < endTime) {
            IDXGIResource* pDesktopResource = nullptr;
            DXGI_OUTDUPL_FRAME_INFO frameInfo;
            
            HRESULT hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);
            
            if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
                timeoutCount++;
            } else if (SUCCEEDED(hr)) {
                successCount++;
                SAFE_RELEASE(pDesktopResource);
                m_pDeskDupl->ReleaseFrame();
                
                // 打印帧信息
                if (successCount <= 10) {
                    std::cout << "Frame " << successCount << ": LastPresentTime=" << frameInfo.LastPresentTime.QuadPart
                              << ", AccumulatedFrames=" << frameInfo.AccumulatedFrames
                              << ", RectsCount=" << frameInfo.TotalMetadataBufferSize << std::endl;
                }
            } else {
                errorCount++;
                std::cerr << "Error acquiring frame: 0x" << std::hex << hr << std::endl;
            }
            
            // 不要延迟，测试最大获取速度
        }
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::high_resolution_clock::now() - startTime);
        
        std::cout << "\n=== Results (5 seconds) ===" << std::endl;
        std::cout << "Successful frames: " << successCount << std::endl;
        std::cout << "Timeout count: " << timeoutCount << std::endl;
        std::cout << "Error count: " << errorCount << std::endl;
        std::cout << "Effective FPS: " << (successCount * 1000.0 / duration.count()) << std::endl;
        std::cout << "Total attempts: " << (successCount + timeoutCount + errorCount) << std::endl;
    }
    
    void TestWithDifferentTimeouts() {
        std::cout << "\n=== Testing Different Timeouts ===" << std::endl;
        
        UINT timeouts[] = {0, 1, 5, 16, 33}; // 0ms, 1ms, 5ms, 16ms(60fps), 33ms(30fps)
        
        for (UINT timeout : timeouts) {
            std::cout << "\nTesting with " << timeout << "ms timeout:" << std::endl;
            
            int frameCount = 0;
            auto startTime = std::chrono::high_resolution_clock::now();
            auto endTime = startTime + std::chrono::seconds(2);
            
            while (std::chrono::high_resolution_clock::now() < endTime) {
                IDXGIResource* pDesktopResource = nullptr;
                DXGI_OUTDUPL_FRAME_INFO frameInfo;
                
                HRESULT hr = m_pDeskDupl->AcquireNextFrame(timeout, &frameInfo, &pDesktopResource);
                
                if (SUCCEEDED(hr)) {
                    frameCount++;
                    SAFE_RELEASE(pDesktopResource);
                    m_pDeskDupl->ReleaseFrame();
                } else if (hr != DXGI_ERROR_WAIT_TIMEOUT) {
                    std::cerr << "Error: 0x" << std::hex << hr << std::endl;
                    break;
                }
            }
            
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::high_resolution_clock::now() - startTime);
            double fps = frameCount * 1000.0 / duration.count();
            
            std::cout << "  Frames: " << frameCount << ", FPS: " << fps << std::endl;
        }
    }
    
    void TestCursorMovement() {
        std::cout << "\n=== Testing with Cursor Movement ===" << std::endl;
        std::cout << "Move your mouse cursor around for 5 seconds..." << std::endl;
        
        int frameCount = 0;
        auto startTime = std::chrono::high_resolution_clock::now();
        auto endTime = startTime + std::chrono::seconds(5);
        
        while (std::chrono::high_resolution_clock::now() < endTime) {
            IDXGIResource* pDesktopResource = nullptr;
            DXGI_OUTDUPL_FRAME_INFO frameInfo;
            
            HRESULT hr = m_pDeskDupl->AcquireNextFrame(1, &frameInfo, &pDesktopResource);
            
            if (SUCCEEDED(hr)) {
                frameCount++;
                SAFE_RELEASE(pDesktopResource);
                m_pDeskDupl->ReleaseFrame();
            }
        }
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::high_resolution_clock::now() - startTime);
        double fps = frameCount * 1000.0 / duration.count();
        
        std::cout << "Frames with cursor movement: " << frameCount << ", FPS: " << fps << std::endl;
    }
};

int main() {
    std::cout << "=== DXGI Desktop Duplication Diagnosis ===" << std::endl;
    
    CaptureDebugger debugger;
    
    if (!debugger.Initialize()) {
        std::cerr << "Failed to initialize debugger" << std::endl;
        return -1;
    }
    
    // 测试不同场景
    debugger.TestFrameAcquisition();
    debugger.TestWithDifferentTimeouts();
    debugger.TestCursorMovement();
    
    std::cout << "\n=== Diagnosis Complete ===" << std::endl;
    std::cout << "This will help us understand why DXGI isn't providing frames at 180Hz" << std::endl;
    
    return 0;
}
