#pragma once

#include "Common.h"

/**
 * 高性能屏幕截图类
 * 使用DXGI进行硬件加速截图，支持图像预处理
 */
class ScreenCapture {
public:
    /**
     * 构造函数
     * @param roiX ROI区域左上角X坐标 (-1表示屏幕中心)
     * @param roiY ROI区域左上角Y坐标 (-1表示屏幕中心)
     * @param roiWidth ROI区域宽度 (-1表示默认640)
     * @param roiHeight ROI区域高度 (-1表示默认640)
     * @param targetSize 输出图像尺寸 (默认320x320)
     */
    ScreenCapture(int roiX = -1, int roiY = -1, int roiWidth = -1, int roiHeight = -1, int targetSize = 320);
    
    /**
     * 析构函数
     */
    ~ScreenCapture();

    /**
     * 初始化截图器
     * @return 成功返回Success，失败返回相应错误码
     */
    ErrorCode Initialize();

    /**
     * 截图并预处理
     * @return 预处理后的RGB图像(targetSize x targetSize)，失败返回空Mat
     */
    cv::Mat CaptureAndPreprocess();

    /**
     * 获取原始截图(BGRA格式)
     * @return 原始BGRA图像，失败返回空Mat
     */
    cv::Mat CaptureRaw();

    /**
     * 释放资源
     */
    void Release();

    /**
     * 检查是否已初始化
     */
    bool IsInitialized() const { return m_initialized; }

    /**
     * 获取截图区域信息
     */
    void GetROI(int& x, int& y, int& width, int& height) const {
        x = m_roiX; y = m_roiY; width = m_roiWidth; height = m_roiHeight;
    }

    /**
     * 获取性能统计
     */
    PerformanceStats GetPerformanceStats() const { return m_stats; }

private:
    // DXGI相关成员
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    // 配置参数
    int m_screenWidth, m_screenHeight;
    int m_roiX, m_roiY, m_roiWidth, m_roiHeight;
    int m_targetSize;
    bool m_useROI;
    bool m_initialized;
    
    // 图像缓冲区
    std::vector<BYTE> m_frameBuffer;
    
    // 性能统计
    mutable PerformanceStats m_stats;
    mutable TimePoint m_startTime;
    
    // 私有方法
    ErrorCode InitializeDXGI();
    bool CreateStagingTexture();
    HRESULT GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height);
    void PreallocateBuffers();
    void UpdatePerformanceStats(double captureTime, double preprocessTime) const;
    void StartTimer() const;
    double EndTimer() const;
};

/**
 * 截图器工厂类
 */
class ScreenCaptureFactory {
public:
    /**
     * 创建屏幕截图器
     */
    static std::unique_ptr<ScreenCapture> CreateScreenCapture(
        int roiX = -1, int roiY = -1, int roiWidth = -1, int roiHeight = -1, int targetSize = 320);
    
    /**
     * 检查系统是否支持DXGI截图
     */
    static bool IsDXGISupported();
};
