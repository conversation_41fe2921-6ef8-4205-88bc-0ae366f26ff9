{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/OpenCVConfig.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVModules.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVModules-debug.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVModules-release.cmake"}, {"path": "pybind11/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDependentOption.cmake"}, {"path": "pybind11/tools/pybind11Common.cmake"}, {"path": "pybind11/tools/pybind11NewTools.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CheckCXXCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckFlagCommonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"path": "pybind11/tools/JoinPaths.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Desktop/foye_111111/build", "source": "C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller"}, "version": {"major": 1, "minor": 0}}