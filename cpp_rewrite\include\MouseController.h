#pragma once

#include "Common.h"

/**
 * 鼠标控制器基类
 */
class MouseController {
public:
    virtual ~MouseController() = default;
    
    /**
     * 初始化鼠标控制器
     */
    virtual ErrorCode Initialize() = 0;
    
    /**
     * 移动鼠标
     * @param dx X方向移动距离
     * @param dy Y方向移动距离
     * @return 成功返回true
     */
    virtual bool MoveMouse(int dx, int dy) = 0;
    
    /**
     * 按下鼠标左键
     */
    virtual bool MouseLeftDown() = 0;
    
    /**
     * 松开鼠标左键
     */
    virtual bool MouseLeftUp() = 0;
    
    /**
     * 检查按键是否按下
     * @param keyCode 按键码
     * @return 按下返回true
     */
    virtual bool IsKeyPressed(int keyCode) = 0;
    
    /**
     * 释放资源
     */
    virtual void Release() = 0;
    
    /**
     * 获取控制器类型名称
     */
    virtual std::string GetTypeName() const = 0;
};

/**
 * 系统API鼠标控制器
 */
class SystemMouseController : public MouseController {
public:
    SystemMouseController() = default;
    ~SystemMouseController() override = default;
    
    ErrorCode Initialize() override;
    bool MoveMouse(int dx, int dy) override;
    bool MouseLeftDown() override;
    bool MouseLeftUp() override;
    bool IsKeyPressed(int keyCode) override;
    void Release() override;
    std::string GetTypeName() const override { return "System"; }
};

/**
 * KMBox硬件鼠标控制器
 */
class KMBoxMouseController : public MouseController {
public:
    KMBoxMouseController(const std::string& ip, const std::string& port, const std::string& mac);
    ~KMBoxMouseController() override = default;
    
    ErrorCode Initialize() override;
    bool MoveMouse(int dx, int dy) override;
    bool MouseLeftDown() override;
    bool MouseLeftUp() override;
    bool IsKeyPressed(int keyCode) override;
    void Release() override;
    std::string GetTypeName() const override { return "KMBox"; }

private:
    std::string m_ip;
    std::string m_port;
    std::string m_mac;
    bool m_initialized;
    
    // KMBox相关函数指针 (动态加载DLL)
    typedef bool (*InitKMBoxFunc)(const char*, const char*, const char*);
    typedef bool (*MoveMouseFunc)(int, int);
    typedef bool (*MouseDownFunc)();
    typedef bool (*MouseUpFunc)();
    typedef bool (*IsKeyPressedFunc)(int);
    typedef void (*CleanupFunc)();
    
    HMODULE m_kmboxDll;
    InitKMBoxFunc m_initFunc;
    MoveMouseFunc m_moveFunc;
    MouseDownFunc m_mouseDownFunc;
    MouseUpFunc m_mouseUpFunc;
    IsKeyPressedFunc m_keyPressedFunc;
    CleanupFunc m_cleanupFunc;
};

/**
 * GHub鼠标控制器
 */
class GHubMouseController : public MouseController {
public:
    GHubMouseController() = default;
    ~GHubMouseController() override = default;
    
    ErrorCode Initialize() override;
    bool MoveMouse(int dx, int dy) override;
    bool MouseLeftDown() override;
    bool MouseLeftUp() override;
    bool IsKeyPressed(int keyCode) override;
    void Release() override;
    std::string GetTypeName() const override { return "GHub"; }

private:
    bool m_initialized;
    HMODULE m_ghubDll;
    
    // GHub相关函数指针
    typedef bool (*GHubMoveFunc)(int, int);
    typedef bool (*GHubMouseDownFunc)();
    typedef bool (*GHubMouseUpFunc)();
    
    GHubMoveFunc m_moveFunc;
    GHubMouseDownFunc m_mouseDownFunc;
    GHubMouseUpFunc m_mouseUpFunc;
};

/**
 * 鼠标控制器工厂
 */
class MouseControllerFactory {
public:
    /**
     * 创建鼠标控制器
     * @param type 控制器类型 ("system", "kmbox", "ghub")
     * @param config 鼠标配置
     * @return 控制器智能指针
     */
    static std::unique_ptr<MouseController> CreateMouseController(
        const std::string& type,
        const MouseConfig& config
    );
};
