#include "../include/ConfigManager.h"

ConfigManager::ConfigManager() {
    SetDefaultConfig();
}

ErrorCode ConfigManager::LoadConfig(const std::string& configPath) {
    std::ifstream file(configPath);
    if (!file.is_open()) {
        LOG_ERROR("Failed to open config file: " << configPath);
        LOG_INFO("Using default configuration");
        return ErrorCode::Success; // 使用默认配置不算错误
    }

    std::string content((std::istreambuf_iterator<char>(file)),
                        std::istreambuf_iterator<char>());
    file.close();

    try {
        ParseConfigFile(content);
        LOG_INFO("Configuration loaded successfully from: " << configPath);
        return ErrorCode::Success;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to parse config file: " << e.what());
        LOG_INFO("Using default configuration");
        return ErrorCode::ConfigLoadFailed;
    }
}

ErrorCode ConfigManager::SaveConfig(const std::string& configPath) {
    std::ofstream file(configPath);
    if (!file.is_open()) {
        LOG_ERROR("Failed to create config file: " << configPath);
        return ErrorCode::ConfigLoadFailed;
    }

    std::string content = GenerateConfigFile();
    file << content;
    file.close();

    LOG_INFO("Configuration saved to: " << configPath);
    return ErrorCode::Success;
}

void ConfigManager::UpdatePIDParams(const std::string& moveMode, const PIDParams& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_pidParams[moveMode] = params;
    LOG_INFO("PID parameters updated for mode: " << moveMode);
}

PIDParams ConfigManager::GetPIDParams(const std::string& moveMode) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_pidParams.find(moveMode);
    if (it != m_pidParams.end()) {
        return it->second;
    }
    
    // 返回默认参数
    PIDParams defaultParams;
    LOG_ERROR("PID parameters not found for mode: " << moveMode << ", using defaults");
    return defaultParams;
}

void ConfigManager::PrintConfigSummary() const {
    LOG_INFO("=== Configuration Summary ===");
    LOG_INFO("Model: " << m_modelConfig.modelPath);
    LOG_INFO("Model Type: " << m_modelConfig.modelType);
    LOG_INFO("Input Size: " << m_modelConfig.inputSize);
    LOG_INFO("Confidence Threshold: " << m_modelConfig.confidenceThreshold);
    LOG_INFO("NMS Threshold: " << m_modelConfig.nmsThreshold);
    
    LOG_INFO("Capture Method: " << m_captureConfig.method);
    LOG_INFO("Screen Resolution: " << m_captureConfig.screenWidth << "x" << m_captureConfig.screenHeight);
    LOG_INFO("ROI Size: " << m_captureConfig.roiSize);
    
    LOG_INFO("Mouse Mode: " << m_mouseConfig.moveMode);
    if (m_mouseConfig.moveMode == "kmbox") {
        LOG_INFO("KMBox IP: " << m_mouseConfig.kmboxIP);
        LOG_INFO("KMBox Port: " << m_mouseConfig.kmboxPort);
    }
    
    LOG_INFO("Aim Circle Radius: " << m_aimConfig.aimCircleRadius);
    LOG_INFO("Show Preview: " << (m_displayConfig.showPreview ? "Yes" : "No"));
    
    // 打印PID参数
    for (const auto& pair : m_pidParams) {
        const auto& params = pair.second;
        LOG_INFO("PID [" << pair.first << "]: Kp=" << params.kp 
                 << ", Ki=" << params.ki << ", Kd=" << params.kd);
    }
    LOG_INFO("=============================");
}

void ConfigManager::ParseConfigFile(const std::string& content) {
    // 简单的配置文件解析 (key=value格式)
    std::istringstream stream(content);
    std::string line;
    
    while (std::getline(stream, line)) {
        // 跳过注释和空行
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }
        
        size_t pos = line.find('=');
        if (pos == std::string::npos) {
            continue;
        }
        
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);
        
        // 去除空格
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        // 解析配置项
        if (key == "model_path") m_modelConfig.modelPath = value;
        else if (key == "model_type") m_modelConfig.modelType = value;
        else if (key == "confidence_threshold") m_modelConfig.confidenceThreshold = std::stof(value);
        else if (key == "nms_threshold") m_modelConfig.nmsThreshold = std::stof(value);
        else if (key == "input_size") m_modelConfig.inputSize = std::stoi(value);
        else if (key == "num_classes") m_modelConfig.numClasses = std::stoi(value);
        
        else if (key == "capture_method") m_captureConfig.method = value;
        else if (key == "screen_width") m_captureConfig.screenWidth = std::stoi(value);
        else if (key == "screen_height") m_captureConfig.screenHeight = std::stoi(value);
        else if (key == "roi_size") m_captureConfig.roiSize = std::stoi(value);
        
        else if (key == "mouse_mode") m_mouseConfig.moveMode = value;
        else if (key == "kmbox_ip") m_mouseConfig.kmboxIP = value;
        else if (key == "kmbox_port") m_mouseConfig.kmboxPort = value;
        else if (key == "kmbox_mac") m_mouseConfig.kmboxMAC = value;
        
        else if (key == "aim_circle_radius") m_aimConfig.aimCircleRadius = std::stoi(value);
        else if (key == "aim_key1") m_aimConfig.aimKey1 = std::stoi(value);
        else if (key == "aim_key2") m_aimConfig.aimKey2 = std::stoi(value);
        else if (key == "offset_y1") m_aimConfig.offsetY1 = std::stof(value);
        else if (key == "offset_y2") m_aimConfig.offsetY2 = std::stof(value);
        
        else if (key == "show_preview") m_displayConfig.showPreview = (value == "true" || value == "1");
        else if (key == "font_path") m_displayConfig.fontPath = value;
        else if (key == "font_size") m_displayConfig.fontSize = std::stoi(value);
        
        // PID参数解析
        else if (key.find("pid_") == 0) {
            size_t underscorePos = key.find('_', 4);
            if (underscorePos != std::string::npos) {
                std::string mode = key.substr(4, underscorePos - 4);
                std::string param = key.substr(underscorePos + 1);
                
                if (param == "kp") m_pidParams[mode].kp = std::stof(value);
                else if (param == "ki") m_pidParams[mode].ki = std::stof(value);
                else if (param == "kd") m_pidParams[mode].kd = std::stof(value);
                else if (param == "base_ki") m_pidParams[mode].baseKi = std::stof(value);
                else if (param == "max_ki") m_pidParams[mode].maxKi = std::stof(value);
                else if (param == "inner_threshold") m_pidParams[mode].innerThreshold = std::stof(value);
                else if (param == "outer_threshold") m_pidParams[mode].outerThreshold = std::stof(value);
                else if (param == "control_frequency") m_pidParams[mode].controlFrequency = std::stoi(value);
            }
        }
    }
}

std::string ConfigManager::GenerateConfigFile() const {
    std::ostringstream oss;
    
    oss << "# AimAssist Configuration File\n";
    oss << "# Generated automatically\n\n";
    
    oss << "# Model Configuration\n";
    oss << "model_path=" << m_modelConfig.modelPath << "\n";
    oss << "model_type=" << m_modelConfig.modelType << "\n";
    oss << "confidence_threshold=" << m_modelConfig.confidenceThreshold << "\n";
    oss << "nms_threshold=" << m_modelConfig.nmsThreshold << "\n";
    oss << "input_size=" << m_modelConfig.inputSize << "\n";
    oss << "num_classes=" << m_modelConfig.numClasses << "\n\n";
    
    oss << "# Capture Configuration\n";
    oss << "capture_method=" << m_captureConfig.method << "\n";
    oss << "screen_width=" << m_captureConfig.screenWidth << "\n";
    oss << "screen_height=" << m_captureConfig.screenHeight << "\n";
    oss << "roi_size=" << m_captureConfig.roiSize << "\n\n";
    
    oss << "# Mouse Configuration\n";
    oss << "mouse_mode=" << m_mouseConfig.moveMode << "\n";
    oss << "kmbox_ip=" << m_mouseConfig.kmboxIP << "\n";
    oss << "kmbox_port=" << m_mouseConfig.kmboxPort << "\n";
    oss << "kmbox_mac=" << m_mouseConfig.kmboxMAC << "\n\n";
    
    oss << "# Aim Configuration\n";
    oss << "aim_circle_radius=" << m_aimConfig.aimCircleRadius << "\n";
    oss << "aim_key1=" << m_aimConfig.aimKey1 << "\n";
    oss << "aim_key2=" << m_aimConfig.aimKey2 << "\n";
    oss << "offset_y1=" << m_aimConfig.offsetY1 << "\n";
    oss << "offset_y2=" << m_aimConfig.offsetY2 << "\n\n";
    
    oss << "# Display Configuration\n";
    oss << "show_preview=" << (m_displayConfig.showPreview ? "true" : "false") << "\n";
    oss << "font_path=" << m_displayConfig.fontPath << "\n";
    oss << "font_size=" << m_displayConfig.fontSize << "\n\n";
    
    oss << "# PID Parameters\n";
    for (const auto& pair : m_pidParams) {
        const std::string& mode = pair.first;
        const PIDParams& params = pair.second;
        
        oss << "pid_" << mode << "_kp=" << params.kp << "\n";
        oss << "pid_" << mode << "_ki=" << params.ki << "\n";
        oss << "pid_" << mode << "_kd=" << params.kd << "\n";
        oss << "pid_" << mode << "_base_ki=" << params.baseKi << "\n";
        oss << "pid_" << mode << "_max_ki=" << params.maxKi << "\n";
        oss << "pid_" << mode << "_inner_threshold=" << params.innerThreshold << "\n";
        oss << "pid_" << mode << "_outer_threshold=" << params.outerThreshold << "\n";
        oss << "pid_" << mode << "_control_frequency=" << params.controlFrequency << "\n";
    }
    
    return oss.str();
}

void ConfigManager::SetDefaultConfig() {
    // 模型配置
    m_modelConfig.modelPath = "models/PUBGV8_320.onnx";
    m_modelConfig.modelType = "yolov8";
    m_modelConfig.confidenceThreshold = 0.65f;
    m_modelConfig.nmsThreshold = 0.2f;
    m_modelConfig.inputSize = 320;
    m_modelConfig.numClasses = 2;
    m_modelConfig.classNames = {"head", "body"};
    
    // 截图配置
    m_captureConfig.method = "screen";
    m_captureConfig.screenWidth = 1920;
    m_captureConfig.screenHeight = 1080;
    m_captureConfig.roiSize = 640;
    
    // 鼠标配置
    m_mouseConfig.moveMode = "system";
    m_mouseConfig.kmboxIP = "*************";
    m_mouseConfig.kmboxPort = "8808";
    m_mouseConfig.kmboxMAC = "62587019";
    
    // 瞄准配置
    m_aimConfig.aimKey1 = 0x05; // 鼠标侧键
    m_aimConfig.aimKey2 = 0x01; // 鼠标左键
    m_aimConfig.offsetY1 = 0.3f;
    m_aimConfig.offsetY2 = 0.4f;
    m_aimConfig.aimCircleRadius = 90;
    
    // 显示配置
    m_displayConfig.showPreview = true;
    m_displayConfig.fontPath = "C:/Windows/Fonts/simhei.ttf";
    m_displayConfig.fontSize = 20;
    
    // PID参数
    PIDParams systemPID;
    systemPID.kp = 50.0f;
    systemPID.ki = 0.0f;
    systemPID.kd = 0.005f;
    systemPID.controlFrequency = 500;
    m_pidParams["system"] = systemPID;
    
    PIDParams kmboxPID;
    kmboxPID.kp = 50.0f;
    kmboxPID.ki = 0.0f;
    kmboxPID.kd = 0.05f;
    kmboxPID.controlFrequency = 500;
    m_pidParams["kmbox"] = kmboxPID;
    
    PIDParams ghubPID;
    ghubPID.kp = 50.0f;
    ghubPID.ki = 0.0f;
    ghubPID.kd = 0.005f;
    ghubPID.controlFrequency = 500;
    m_pidParams["ghub"] = ghubPID;
}
