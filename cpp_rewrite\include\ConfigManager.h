#pragma once

#include "Common.h"
#include <fstream>
#include <map>
#include <sstream>

/**
 * 配置管理器
 * 负责加载、保存和管理所有配置参数
 */
class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager() = default;

    // 加载配置文件
    ErrorCode LoadConfig(const std::string& configPath = "config.json");
    
    // 保存配置文件
    ErrorCode SaveConfig(const std::string& configPath = "config.json");
    
    // 热更新PID参数
    void UpdatePIDParams(const std::string& moveMode, const PIDParams& params);
    
    // 获取配置
    const ModelConfig& GetModelConfig() const { return m_modelConfig; }
    const CaptureConfig& GetCaptureConfig() const { return m_captureConfig; }
    const MouseConfig& GetMouseConfig() const { return m_mouseConfig; }
    const DisplayConfig& GetDisplayConfig() const { return m_displayConfig; }
    const AimConfig& GetAimConfig() const { return m_aimConfig; }
    
    // 获取PID参数
    PIDParams GetPIDParams(const std::string& moveMode) const;
    
    // 设置配置
    void SetModelConfig(const ModelConfig& config) { m_modelConfig = config; }
    void SetCaptureConfig(const CaptureConfig& config) { m_captureConfig = config; }
    void SetMouseConfig(const MouseConfig& config) { m_mouseConfig = config; }
    void SetDisplayConfig(const DisplayConfig& config) { m_displayConfig = config; }
    void SetAimConfig(const AimConfig& config) { m_aimConfig = config; }
    
    // 打印配置摘要
    void PrintConfigSummary() const;

private:
    ModelConfig m_modelConfig;
    CaptureConfig m_captureConfig;
    MouseConfig m_mouseConfig;
    DisplayConfig m_displayConfig;
    AimConfig m_aimConfig;
    
    // 不同移动方式的PID参数
    std::map<std::string, PIDParams> m_pidParams;

    // 线程安全
    mutable std::mutex m_mutex;
    
    // 简单的配置文件解析
    void ParseConfigFile(const std::string& content);
    std::string GenerateConfigFile() const;
    
    // 默认配置
    void SetDefaultConfig();
};
